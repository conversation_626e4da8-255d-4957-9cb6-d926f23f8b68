from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QTextBrowser, QMessageBox,
                             QFileDialog, QLabel, QPushButton, QHBoxLayout)
from PyQt5.QtCore import Qt
from plugins.base import PluginBase
import re
import os
from datetime import datetime
import html
from difflib import SequenceMatcher

class LogAnalyzerPlugin(PluginBase):
    @property
    def name(self):
        return "EDA日志分析器"

    @property
    def version(self):
        return "2.0.0"

    @property
    def description(self):
        return "分析EDA仿真日志，支持VCS/XRUN，提取错误统计和时序问题"

    def __init__(self):
        super().__init__()
        self.similarity_threshold = 0.8  # 相似度阈值，可调整
        self.error_patterns = {
            # UVM错误模式
            "UVM_ERROR": (r"UVM_ERROR\s*@\s*(\d+(?:\.\d+)?(?:n|p|f)?s):\s*(.*)", "UVM错误"),
            "UVM_FATAL": (r"UVM_FATAL\s*@\s*(\d+(?:\.\d+)?(?:n|p|f)?s):\s*(.*)", "UVM致命错误"),
            "UVM_WARNING": (r"UVM_WARNING\s*@\s*(\d+(?:\.\d+)?(?:n|p|f)?s):\s*(.*)", "UVM警告"),

            # SPRD错误模式
            "SPRD_ERROR": (r"SPRD_ERROR\s*@\s*(\d+(?:\.\d+)?(?:n|p|f)?s):\s*\[(.*?)\]\s*(.*)", "SPRD错误"),
            "SPRD_FATAL": (r"SPRD_FATAL\s*@\s*(\d+(?:\.\d+)?(?:n|p|f)?s):\s*\[(.*?)\]\s*(.*)", "SPRD致命错误"),
            "SPRD_WARNING": (r"SPRD_WARNING\s*@\s*(\d+(?:\.\d+)?(?:n|p|f)?s):\s*\[(.*?)\]\s*(.*)", "SPRD警告"),

            # XRUN特有错误
            "XRUN_ASSERT": (r"\*E,\s*Assertion\s+Error,\s*\[(.*?):([\d]+)\]", "XRUN断言错误"),
            "XRUN_ERROR": (r"ncsim:\s*\*E,.*", "XRUN仿真器错误"),

            # VCS特有错误
            "VCS_ASSERT": (r"\*\*\s*Error:\s*\[(.*?):([\d]+)\]\s*Assertion\s+Error", "VCS断言错误"),
            "VCS_CDC": (r"Error:\s*\"(.*?)\"\s*line\s*(\d+):\s*Assertion\s+Error", "CDC断言错误"),

            # 时序错误
            "SDC_FP": (r"\[(\d+)\]\s*\[FP\].*?@\s*Time:\s*(\d+(?:\.\d+)?(?:n|p|f)?s)", "SDC假路径错误"),
            "SDC_MCP": (r"\[(\d+)\]\s*\[MCP\].*?@\s*Time:\s*(\d+(?:\.\d+)?(?:n|p|f)?s)", "SDC多时钟路径错误")
        }

        # 对话框引用
        self.current_dialog = None

    def initialize(self, main_window):
        """初始化插件时创建菜单项"""
        try:
            self.main_window = main_window

            # 检查主窗口是否有工具菜单
            if not hasattr(main_window, 'tools_menu'):
                print("错误: 主窗口没有tools_menu属性")
                return

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.setCheckable(False)
            self.menu_action.triggered.connect(self.analyze_current_log)

            # 添加分隔符和菜单项
            self.main_window.tools_menu.addSeparator()
            self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            from PyQt5.QtWidgets import QLabel, QToolButton

            # 创建状态指示器标签
            self.status_label = QLabel("EDA日志分析器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def analyze_current_log(self):
        """分析当前标签页的日志或让用户选择日志文件"""
        sim_log_path = None
        case_name = ""

        # 尝试从主窗口获取当前用例信息
        try:
            # 检查是否有执行控制器
            if hasattr(self.main_window, 'execution_controller'):
                # 尝试获取当前用例名称
                if hasattr(self.main_window.execution_controller, 'current_case'):
                    case_name = self.main_window.execution_controller.current_case

            # 如果没有找到执行控制器，尝试从右侧面板获取
            elif hasattr(self.main_window, 'right_panel'):
                # 尝试获取当前标签页
                if hasattr(self.main_window.right_panel, 'currentWidget'):
                    current_tab = self.main_window.right_panel.currentWidget()
                    if hasattr(current_tab, 'case_name'):
                        case_name = current_tab.case_name
        except Exception as e:
            print(f"获取当前用例信息失败: {str(e)}")

        # 如果有用例名称，检查常见的仿真log文件
        if case_name:
            # 检查常见的仿真log文件名
            possible_logs = [
                f"{case_name}/log/irun_sim.log",  # XRUN log
                f"{case_name}/log/vcs_sim.log",   # VCS log
                f"{case_name}/log/sim.log",       # 通用log
            ]

            # 尝试找到第一个存在的log文件
            for log_path in possible_logs:
                if os.path.exists(log_path):
                    sim_log_path = log_path
                    break

        # 如果没有找到日志文件，让用户选择
        if not sim_log_path:
            # 设置初始目录：如果有case_name就用对应目录，否则用当前目录
            initial_dir = case_name if case_name else "."
            sim_log_path = QFileDialog.getOpenFileName(
                self.main_window,
                "选择仿真日志文件",
                initial_dir,
                "Log文件 (*.log);;所有文件 (*.*)"
            )[0]

            if not sim_log_path:  # 用户取消选择
                return

        try:
            # 读取仿真日志内容
            try:
                with open(sim_log_path, 'r', encoding='utf-8', errors='ignore') as f:
                    log_content = f.read()
            except Exception as e:
                QMessageBox.critical(self.main_window, "错误", f"读取日志文件失败：{str(e)}")
                return

            if not log_content.strip():
                QMessageBox.warning(self.main_window, "警告", "仿真日志内容为空")
                return

            # 分析日志
            results = self.analyze_log(log_content)
            if not results:
                QMessageBox.information(self.main_window, "提示", "未发现任何错误或警告")
                return

            # 在结果中添加文件路径信息
            results.insert(1, f"分析文件: {sim_log_path}\n")

            # 显示结果
            self.show_results(results)

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"分析日志时出错：{str(e)}")
            print(f"日志分析错误: {str(e)}")

    def analyze_log(self, content):
        """分析日志内容，提取所有类型的错误信息"""
        results = []

        try:
            # 错误计数器和详细信息存储
            error_counts = {}
            detailed_errors = {}

            # 初始化所有错误类型的计数器
            for pattern_name, (_, label) in self.error_patterns.items():
                error_counts[label] = 0
                detailed_errors[label] = []

            # 按行分析并进行错误匹配
            lines = content.splitlines()
            i = 0
            while i < len(lines):
                line = lines[i].strip()
                context_lines = []

                # 获取上下文（前后5行）
                start_idx = max(0, i - 5)
                end_idx = min(len(lines), i + 6)
                context_lines = lines[start_idx:end_idx]

                # 检查每种错误模式
                for pattern_name, (regex, label) in self.error_patterns.items():
                    match = re.search(regex, line, re.IGNORECASE)
                    if match:
                        error_counts[label] += 1

                        # 根据不同的错误类型处理匹配组
                        error_info = self.format_error_info(pattern_name, match, context_lines)
                        if error_info not in detailed_errors[label]:  # 避免重复错误
                            detailed_errors[label].append(error_info)
                        break

                # 特殊处理SDC错误的后续行
                if any(sdc_pattern in line for sdc_pattern in ['[FP]', '[MCP]']):
                    while i + 1 < len(lines) and lines[i + 1].strip():
                        context_lines.append(lines[i + 1])
                        i += 1

                i += 1

            # 合并相似错误
            detailed_errors = self.merge_similar_errors(detailed_errors)

            # 生成分析报告
            results.append("=== EDA仿真日志分析报告 ===\n")
            results.append(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            # 计算总错误数
            total_errors = sum(count for label, count in error_counts.items()
                             if '错误' in label or '致命' in label)
            total_warnings = sum(count for label, count in error_counts.items()
                               if '警告' in label)

            if total_errors > 0 or total_warnings > 0:
                results.append("【错误统计摘要】")
                if total_errors > 0:
                    results.append(f"总错误数: {total_errors}")
                if total_warnings > 0:
                    results.append(f"总警告数: {total_warnings}")
                results.append("")

                # 按错误类型显示详细信息
                for label, messages in detailed_errors.items():
                    if messages:
                        count = error_counts[label]
                        if count > 0:
                            results.append(f"\n=== {label} (共 {count} 个) ===")
                            for msg in messages:
                                # 处理重复错误信息
                                if msg.startswith("以下错误出现了"):
                                    results.append(msg)
                                else:
                                    # 分开错误信息和上下文信息
                                    parts = msg.split("上下文信息:\n", 1)
                                    results.append(parts[0])  # 错误基本信息
                                    if len(parts) > 1:
                                        results.append("上下文信息:")
                                        results.append(parts[1].strip())
                                # 在每个完整的错误+上下文信息块之后添加分隔线
                                results.append("-" * 80)
            else:
                results.append("未发现任何错误或警告\n")

        except Exception as e:
            print(f"分析日志时出错: {str(e)}")
            results.append(f"分析过程出错: {str(e)}")

        return results

    def format_error_info(self, pattern_name, match, context_lines):
        """根据不同的错误类型格式化错误信息"""
        # 先格式化基本信息
        if pattern_name in ["UVM_ERROR", "UVM_FATAL", "UVM_WARNING"]:
            basic_info = f"时间: {match.group(1)}\n错误信息: {match.group(2)}"
        elif pattern_name in ["SPRD_ERROR", "SPRD_FATAL", "SPRD_WARNING"]:
            basic_info = f"时间: {match.group(1)}\n测试: {match.group(2)}\n错误信息: {match.group(3)}"
        elif pattern_name in ["XRUN_ASSERT", "VCS_ASSERT"]:
            basic_info = f"文件: {match.group(1)}\n行号: {match.group(2)}"
        elif pattern_name == "VCS_CDC":
            basic_info = f"文件: {match.group(1)}\n行号: {match.group(2)}"
        elif pattern_name in ["SDC_FP", "SDC_MCP"]:
            basic_info = f"编号: {match.group(1)}\n时间: {match.group(2)}"
        else:
            basic_info = "错误信息: 未知类型"

        # 添加上下文信息，保持原始格式
        context_info = "上下文信息:\n"
        for line in context_lines:
            if line.strip():  # 只添加非空行
                context_info += line + "\n"

        return f"{basic_info}\n{context_info}"

    def calculate_similarity(self, str1, str2):
        """计算两个字符串的相似度"""
        return SequenceMatcher(None, str1, str2).ratio()

    def merge_similar_errors(self, detailed_errors):
        """合并相似的错误信息"""
        merged_errors = {}

        for label, messages in detailed_errors.items():
            merged_messages = []
            message_groups = []  # 存储相似消息组

            for msg in messages:
                # 提取错误信息部分（跳过时间戳等）
                error_msg = ""
                for line in msg.split('\n'):
                    if line.startswith("错误信息:"):
                        error_msg = line.split(":", 1)[1].strip()
                        break
                if not error_msg:
                    merged_messages.append(msg)
                    continue

                # 查找相似组
                found_group = False
                for group in message_groups:
                    # 检查与组中第一个消息的相似度
                    if self.calculate_similarity(error_msg, group['error_msg']) >= self.similarity_threshold:
                        group['count'] += 1
                        group['messages'].append(msg)
                        found_group = True
                        break

                if not found_group:
                    # 创建新组
                    message_groups.append({
                        'error_msg': error_msg,
                        'count': 1,
                        'messages': [msg]
                    })

            # 处理所有消息组
            for group in message_groups:
                if group['count'] > 1:
                    # 如果有多个相似消息，合并显示
                    first_msg = group['messages'][0]
                    merged_msg = (
                        f"以下错误出现了 {group['count']} 次:\n"
                        f"{first_msg}\n"
                        f"其余 {group['count']-1} 次发生在不同时间点"
                    )
                    merged_messages.append(merged_msg)
                else:
                    # 单个消息直接添加
                    merged_messages.append(group['messages'][0])

            merged_errors[label] = merged_messages

        return merged_errors

    def show_results(self, results):
        """使用美化的HTML格式显示分析结果"""
        # 创建非模态对话框
        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("EDA日志分析结果")
        dialog.resize(1000, 800)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        layout = QVBoxLayout()

        # 创建文本浏览器
        text_browser = QTextBrowser()
        text_browser.setOpenExternalLinks(True)
        text_browser.setStyleSheet("""
            QTextBrowser {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 11pt;
                background-color: #f8f9fa;
                color: #212529;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 10px;
            }
        """)

        # 添加CSS样式
        html_text = ["""
            <style>
                .title { color: #0056b3; font-size: 16pt; font-weight: bold; margin: 10px 0; }
                .summary { background-color: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0; }
                .error-count { color: #dc3545; }
                .warning-count { color: #ffc107; }
                .error-block {
                    margin: 10px 0;
                    padding: 10px;
                    border-left: 4px solid #dc3545;
                    background-color: #fff;
                }
                .warning-block {
                    margin: 10px 0;
                    padding: 10px;
                    border-left: 4px solid #ffc107;
                    background-color: #fff;
                }
                .error-header { color: #dc3545; font-weight: bold; }
                .warning-header { color: #ffc107; font-weight: bold; }
                .info { color: #0056b3; margin: 3px 0; }
                .timestamp { color: #6c757d; font-size: 0.9em; margin: 3px 0; }
                .context-title { color: #495057; font-weight: bold; margin: 5px 0; }
                .context-block {
                    font-family: Consolas, monospace;
                    background-color: #f8f9fa;
                    padding: 10px;
                    border-radius: 4px;
                    margin: 5px 0;
                    white-space: pre;
                    border: 1px solid #e9ecef;
                }
                .error-info-line {
                    white-space: pre;
                    margin: 2px 0;
                    line-height: 1.4;
                }
                .error-separator {
                    margin: 15px 0;
                    border: 0;
                    border-top: 1px solid #dee2e6;
                }
                .occurrence-info {
                    color: #0056b3;
                    font-style: italic;
                    margin: 5px 0;
                }
            </style>
        """]

        current_block = []
        in_context = False
        in_error_section = False

        for line in results:
            if line.startswith("=== EDA仿真日志分析报告"):
                html_text.append('<div class="title">EDA仿真日志分析报告</div>')
            elif line.startswith("分析时间:"):
                html_text.append(f'<div class="timestamp">{html.escape(line)}</div>')
            elif line.startswith("分析文件:"):
                html_text.append(f'<div class="info">{html.escape(line)}</div>')
            elif line == "【错误统计摘要】":
                html_text.append('<div class="summary">')
            elif line.startswith("总错误数:"):
                html_text.append(f'<div class="error-count">{html.escape(line)}</div>')
            elif line.startswith("总警告数:"):
                html_text.append(f'<div class="warning-count">{html.escape(line)}</div>')
                html_text.append('</div>')
            elif line.startswith("===") and ("错误" in line or "警告" in line):
                # 处理前一个错误块的结束
                if in_error_section:
                    html_text.append('</div>')

                # 开始新的错误块
                in_error_section = True
                count = re.search(r'\(共 (\d+) 个\)', line)
                header = re.sub(r'\(共 \d+ 个\)', '', line).replace('===', '').strip()
                block_class = "error-block" if "错误" in line else "warning-block"
                header_class = "error-header" if "错误" in line else "warning-header"
                count_class = "error-count" if "错误" in line else "warning-count"

                html_text.append(f'<div class="{block_class}">')
                html_text.append(f'<div class="{header_class}">{html.escape(header)}')
                if count:
                    html_text.append(f' <span class="{count_class}">({count.group(1)}个)</span>')
                html_text.append('</div>')
            elif line.startswith("以下错误出现了"):
                occurrence_info = re.match(r"以下错误出现了 (\d+) 次", line)
                if occurrence_info:
                    html_text.append(f'<div class="occurrence-info">⚠️ 此错误重复出现 {occurrence_info.group(1)} 次</div>')
                    current_block = []
            elif line.startswith("上下文信息:"):
                # 先添加之前收集的错误信息
                if current_block:
                    for error_line in current_block:
                        html_text.append(f'<div class="error-info-line">{html.escape(error_line)}</div>')
                    current_block = []

                html_text.append('<div class="context-title">上下文信息:</div>')
                in_context = True
            elif line.strip() == "-" * 80:  # 分隔线
                if in_context:
                    if current_block:
                        html_text.append('<div class="context-block">')
                        html_text.append(html.escape('\n'.join(current_block)))
                        html_text.append('</div>')
                    in_context = False
                html_text.append('<hr class="error-separator"/>')
                current_block = []
            elif in_context:
                current_block.append(line)
            elif line.strip():
                # 非上下文的错误信息行
                current_block.append(line)

        # 处理最后一个块
        if current_block:
            if in_context:
                html_text.append('<div class="context-block">')
                html_text.append(html.escape('\n'.join(current_block)))
                html_text.append('</div>')
            else:
                for error_line in current_block:
                    html_text.append(f'<div class="error-info-line">{html.escape(error_line)}</div>')

        # 关闭最后一个错误块
        if in_error_section:
            html_text.append('</div>')

        # 将HTML文本设置到浏览器中
        text_browser.setHtml('\n'.join(html_text))
        layout.addWidget(text_browser)

        # 添加底部按钮
        button_layout = QHBoxLayout()

        # 导出按钮
        export_button = QPushButton("导出报告")
        export_button.clicked.connect(lambda: self.export_results(results))

        # 最小化按钮
        minimize_button = QPushButton("最小化窗口")
        minimize_button.clicked.connect(dialog.showMinimized)

        # 后台运行按钮
        background_button = QPushButton("后台运行")
        background_button.clicked.connect(lambda: self.run_in_background(dialog))

        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.close)

        # 添加按钮到布局
        button_layout.addWidget(export_button)
        button_layout.addStretch()
        button_layout.addWidget(minimize_button)
        button_layout.addWidget(background_button)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)
        dialog.setLayout(layout)

        # 连接对话框关闭事件
        dialog.finished.connect(self.on_dialog_closed)

        # 显示状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(True)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(True)

        # 非模态显示对话框
        dialog.show()

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("EDA日志分析器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.current_dialog = None

    def export_results(self, results):
        """导出分析结果到文件"""
        try:
            file_name = QFileDialog.getSaveFileName(
                self.main_window,
                "导出分析报告",
                "",
                "文本文件 (*.txt);;HTML文件 (*.html)"
            )[0]

            if file_name:
                if file_name.endswith('.html'):
                    # 导出为HTML格式
                    html_content = ['<!DOCTYPE html><html><head>',
                                  '<meta charset="utf-8">',
                                  '<style>',
                                  'body { font-family: Consolas, monospace; padding: 20px; }',
                                  'pre { background-color: #f5f5f5; padding: 10px; border: 1px solid #e0e0e0; border-radius: 4px; }',
                                  '.error { color: #dc3545; }',
                                  '.warning { color: #ffc107; }',
                                  '.header { color: #0056b3; }',
                                  '.context { margin: 10px 0; }',
                                  '</style></head><body>']

                    in_context = False
                    context_lines = []

                    for line in results:
                        if line.startswith("上下文信息:"):
                            in_context = True
                            context_lines = []
                            html_content.append('<div class="context"><b>上下文信息:</b></div>')
                            continue
                        elif in_context:
                            if line.strip() == "-" * 80:
                                if context_lines:
                                    html_content.append('<pre>')
                                    html_content.append(html.escape('\n'.join(context_lines)))
                                    html_content.append('</pre>')
                                    html_content.append('<hr/>')
                                in_context = False
                                context_lines = []
                            else:
                                context_lines.append(line)
                            continue

                        # 处理非上下文的内容
                        parts = line.split('\n')
                        for part in parts:
                            if not part:
                                continue
                            if any(keyword in part for keyword in ['错误', '致命', 'Error', 'FATAL']):
                                html_content.append(f'<div class="error">{html.escape(part)}</div>')
                            elif any(keyword in part for keyword in ['警告', 'Warning']):
                                html_content.append(f'<div class="warning">{html.escape(part)}</div>')
                            elif '===' in part:
                                html_content.append(f'<h3 class="header">{html.escape(part)}</h3>')
                            else:
                                html_content.append(f'<div>{html.escape(part)}</div>')

                    html_content.append('</body></html>')

                    with open(file_name, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(html_content))
                else:
                    # 导出为文本格式
                    with open(file_name, 'w', encoding='utf-8') as f:
                        f.write('\n'.join(results))

                QMessageBox.information(self.main_window, "成功", "分析报告导出成功")
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"导出报告时出错：{str(e)}")