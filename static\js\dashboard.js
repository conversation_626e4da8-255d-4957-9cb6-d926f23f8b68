// RunSim 仪表盘系统 JavaScript

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 自动隐藏消息提示
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);

    // 表格行点击事件
    $('.table tbody tr').click(function() {
        $(this).addClass('table-active').siblings().removeClass('table-active');
    });

    // 搜索功能
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.searchable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });

    // 状态过滤
    $('.status-filter').change(function() {
        var status = $(this).val();
        if (status === '') {
            $('.filterable tbody tr').show();
        } else {
            $('.filterable tbody tr').hide();
            $('.filterable tbody tr[data-status="' + status + '"]').show();
        }
    });

    // 项目过滤
    $('.project-filter').change(function() {
        var project = $(this).val();
        if (project === '') {
            $('.filterable tbody tr').show();
        } else {
            $('.filterable tbody tr').hide();
            $('.filterable tbody tr[data-project="' + project + '"]').show();
        }
    });

    // 确认删除对话框
    $('.delete-confirm').click(function(e) {
        e.preventDefault();
        var url = $(this).attr('href');
        var name = $(this).data('name');
        
        if (confirm('确定要删除 "' + name + '" 吗？此操作不可撤销。')) {
            window.location.href = url;
        }
    });

    // AJAX表单提交
    $('.ajax-form').submit(function(e) {
        e.preventDefault();
        var form = $(this);
        var url = form.attr('action');
        var data = form.serialize();
        
        $.ajax({
            url: url,
            type: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    showMessage('success', response.message);
                    if (response.redirect) {
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1000);
                    }
                } else {
                    showMessage('danger', response.message);
                }
            },
            error: function() {
                showMessage('danger', '操作失败，请稍后重试。');
            }
        });
    });

    // 显示消息
    function showMessage(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        $('.container-fluid').prepend(alertHtml);
        
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    }

    // 实时更新时间显示
    function updateTimeDisplay() {
        $('.time-display').each(function() {
            var timestamp = $(this).data('timestamp');
            if (timestamp) {
                var date = new Date(timestamp);
                var now = new Date();
                var diff = now - date;
                var minutes = Math.floor(diff / 60000);
                var hours = Math.floor(minutes / 60);
                var days = Math.floor(hours / 24);
                
                var timeText = '';
                if (days > 0) {
                    timeText = days + '天前';
                } else if (hours > 0) {
                    timeText = hours + '小时前';
                } else if (minutes > 0) {
                    timeText = minutes + '分钟前';
                } else {
                    timeText = '刚刚';
                }
                
                $(this).text(timeText);
            }
        });
    }

    // 每分钟更新一次时间显示
    setInterval(updateTimeDisplay, 60000);
    updateTimeDisplay();

    // 图表颜色主题
    Chart.defaults.color = '#495057';
    Chart.defaults.borderColor = '#dee2e6';
    Chart.defaults.backgroundColor = 'rgba(13, 110, 253, 0.1)';

    // 响应式表格
    function makeTablesResponsive() {
        $('.table').each(function() {
            if (!$(this).parent().hasClass('table-responsive')) {
                $(this).wrap('<div class="table-responsive"></div>');
            }
        });
    }
    makeTablesResponsive();

    // 加载状态管理
    function showLoading(element) {
        element.html('<span class="loading"></span> 加载中...');
        element.prop('disabled', true);
    }

    function hideLoading(element, originalText) {
        element.html(originalText);
        element.prop('disabled', false);
    }

    // 导出功能
    $('.export-btn').click(function(e) {
        e.preventDefault();

        var format = $(this).data('format') || 'excel';
        var url = $(this).data('url');
        var btn = $(this);

        // 如果没有指定URL，根据当前页面确定导出URL
        if (!url) {
            var currentPath = window.location.pathname;
            if (currentPath.includes('testcases')) {
                url = '/api/export/testcases/';
            } else if (currentPath.includes('bugs')) {
                url = '/api/export/bugs/';
            } else {
                alert('无法确定导出类型');
                return;
            }
        }

        // 获取当前的过滤参数
        var params = new URLSearchParams(window.location.search);
        params.set('format', format);

        var exportUrl = url + '?' + params.toString();

        // 显示加载状态
        var originalText = btn.html();
        btn.html('<span class="spinner-border spinner-border-sm me-1"></span>导出中...');
        btn.prop('disabled', true);

        // 创建隐藏的下载链接
        var downloadLink = document.createElement('a');
        downloadLink.href = exportUrl;
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // 恢复按钮状态
        setTimeout(function() {
            btn.html(originalText);
            btn.prop('disabled', false);
        }, 2000);
    });

    // 批量操作
    $('#selectAll').change(function() {
        $('.item-checkbox').prop('checked', $(this).prop('checked'));
        updateBatchActions();
    });

    $('.item-checkbox').change(function() {
        updateBatchActions();
    });

    function updateBatchActions() {
        var checkedCount = $('.item-checkbox:checked').length;
        if (checkedCount > 0) {
            $('.batch-actions').show();
            $('.batch-count').text(checkedCount);
        } else {
            $('.batch-actions').hide();
        }
    }

    // 文件上传预览
    $('.file-input').change(function() {
        var file = this.files[0];
        if (file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $('.file-preview').html('<img src="' + e.target.result + '" class="img-thumbnail" style="max-width: 200px;">');
            };
            reader.readAsDataURL(file);
        }
    });

    // 自动保存草稿
    var autoSaveTimer;
    $('.auto-save').on('input', function() {
        clearTimeout(autoSaveTimer);
        autoSaveTimer = setTimeout(function() {
            saveDraft();
        }, 2000);
    });

    function saveDraft() {
        var formData = $('.auto-save-form').serialize();
        $.ajax({
            url: '/api/save-draft/',
            type: 'POST',
            data: formData,
            success: function() {
                $('.save-status').text('已自动保存').fadeIn().delay(2000).fadeOut();
            }
        });
    }

    // 键盘快捷键
    $(document).keydown(function(e) {
        // Ctrl+S 保存
        if (e.ctrlKey && e.keyCode === 83) {
            e.preventDefault();
            $('.save-btn').click();
        }
        
        // Ctrl+F 搜索
        if (e.ctrlKey && e.keyCode === 70) {
            e.preventDefault();
            $('#searchInput').focus();
        }
        
        // ESC 关闭模态框
        if (e.keyCode === 27) {
            $('.modal').modal('hide');
        }
    });

    // 页面离开确认
    var hasUnsavedChanges = false;
    $('.form-control').change(function() {
        hasUnsavedChanges = true;
    });

    $('.save-btn').click(function() {
        hasUnsavedChanges = false;
    });

    window.addEventListener('beforeunload', function(e) {
        if (hasUnsavedChanges) {
            e.preventDefault();
            e.returnValue = '';
        }
    });
});
