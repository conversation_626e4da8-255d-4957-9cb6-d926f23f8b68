<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}RunSim 仪表盘系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/dashboard.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'dashboard:home' %}">
                <i class="fas fa-tachometer-alt me-2"></i>RunSim 仪表盘
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" 
                           href="{% url 'dashboard:home' %}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'project' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'dashboard:project_list' %}">
                            <i class="fas fa-project-diagram me-1"></i>项目管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'testcase' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'dashboard:testcase_list' %}">
                            <i class="fas fa-tasks me-1"></i>用例管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if 'bug' in request.resolver_match.url_name %}active{% endif %}" 
                           href="{% url 'dashboard:bug_list' %}">
                            <i class="fas fa-bug me-1"></i>BUG管理
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>用户
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/admin/">管理后台</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        {% block content %}{% endblock %}
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% load static %}
    <script src="{% static 'js/dashboard.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
