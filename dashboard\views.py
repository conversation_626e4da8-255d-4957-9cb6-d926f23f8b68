from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count, Q
from django.utils import timezone
from django.core.paginator import Paginator
from django.views.decorators.csrf import csrf_exempt
import json
import os
import pandas as pd
from datetime import datetime, timedelta

from .models import Project, TestPlan, TestCase, Bug, ExecutionRecord, CaseStatusSummary


def dashboard_home(request):
    """仪表盘首页"""
    # 获取项目统计
    total_projects = Project.objects.filter(is_active=True).count()

    # 获取用例统计
    total_testcases = TestCase.objects.count()
    passed_testcases = TestCase.objects.filter(
        Q(subsys_status='pass') | Q(top_status='pass') |
        Q(post_subsys_status='pass') | Q(post_top_status='pass')
    ).count()

    # 获取BUG统计
    total_bugs = Bug.objects.count()
    open_bugs = Bug.objects.filter(status__in=['open', 'in_progress']).count()

    # 获取最近的项目
    recent_projects = Project.objects.filter(is_active=True).order_by('-updated_at')[:5]

    # 获取最近的执行记录
    recent_executions = ExecutionRecord.objects.order_by('-start_time')[:10]

    context = {
        'total_projects': total_projects,
        'total_testcases': total_testcases,
        'passed_testcases': passed_testcases,
        'total_bugs': total_bugs,
        'open_bugs': open_bugs,
        'recent_projects': recent_projects,
        'recent_executions': recent_executions,
    }

    return render(request, 'dashboard/home.html', context)


def project_list(request):
    """项目列表"""
    projects = Project.objects.filter(is_active=True).order_by('-updated_at')

    # 分页
    paginator = Paginator(projects, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }

    return render(request, 'dashboard/project_list.html', context)


def project_detail(request, project_id):
    """项目详情"""
    project = get_object_or_404(Project, id=project_id)

    # 获取项目的测试计划
    testplans = TestPlan.objects.filter(project=project).order_by('-uploaded_at')

    # 获取项目的BUG统计
    bugs = Bug.objects.filter(project=project)
    bug_stats = {
        'total': bugs.count(),
        'open': bugs.filter(status__in=['open', 'in_progress']).count(),
        'resolved': bugs.filter(status='resolved').count(),
        'closed': bugs.filter(status='closed').count(),
    }

    # 获取用例统计
    testcases = TestCase.objects.filter(testplan__project=project)
    case_stats = {
        'total': testcases.count(),
        'subsys_pass': testcases.filter(subsys_status='pass').count(),
        'top_pass': testcases.filter(top_status='pass').count(),
        'post_subsys_pass': testcases.filter(post_subsys_status='pass').count(),
        'post_top_pass': testcases.filter(post_top_status='pass').count(),
    }

    context = {
        'project': project,
        'testplans': testplans,
        'bug_stats': bug_stats,
        'case_stats': case_stats,
    }

    return render(request, 'dashboard/project_detail.html', context)


def testcase_list(request):
    """测试用例列表"""
    testcases = TestCase.objects.all().order_by('-updated_at')

    # 过滤
    project_id = request.GET.get('project')
    if project_id:
        testcases = testcases.filter(testplan__project_id=project_id)

    status = request.GET.get('status')
    if status:
        testcases = testcases.filter(
            Q(subsys_status=status) | Q(top_status=status) |
            Q(post_subsys_status=status) | Q(post_top_status=status)
        )

    # 分页
    paginator = Paginator(testcases, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取项目列表用于过滤
    projects = Project.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'projects': projects,
        'current_project': project_id,
        'current_status': status,
    }

    return render(request, 'dashboard/testcase_list.html', context)


def testcase_detail(request, testcase_id):
    """测试用例详情"""
    testcase = get_object_or_404(TestCase, id=testcase_id)

    # 获取执行历史记录
    execution_records = ExecutionRecord.objects.filter(testcase=testcase).order_by('-start_time')

    context = {
        'testcase': testcase,
        'execution_records': execution_records,
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，返回模态框内容
        return render(request, 'dashboard/testcase_detail_modal.html', context)
    else:
        # 普通请求，返回完整页面
        return render(request, 'dashboard/testcase_detail.html', context)


def edit_testcase(request, testcase_id):
    """编辑测试用例 - 允许所有用户访问"""
    testcase = get_object_or_404(TestCase, id=testcase_id)

    if request.method == 'GET':
        # 返回编辑页面
        context = {'testcase': testcase}
        return render(request, 'dashboard/edit_testcase.html', context)

    elif request.method == 'POST':
        try:
            # 更新用例信息
            testcase.testcase_name = request.POST.get('testcase_name', testcase.testcase_name)
            testcase.test_category = request.POST.get('test_category', testcase.test_category)
            testcase.function_points = request.POST.get('function_points', testcase.function_points)
            testcase.owner = request.POST.get('owner', testcase.owner)
            testcase.subsys_status = request.POST.get('subsys_status', testcase.subsys_status)
            testcase.top_status = request.POST.get('top_status', testcase.top_status)
            testcase.post_subsys_status = request.POST.get('post_subsys_status', testcase.post_subsys_status)
            testcase.post_top_status = request.POST.get('post_top_status', testcase.post_top_status)
            testcase.note = request.POST.get('note', testcase.note)

            testcase.save()

            return JsonResponse({
                'success': True,
                'message': '用例更新成功'
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})

    else:
        return JsonResponse({'success': False, 'message': '不支持的请求方法'})


def bug_list(request):
    """BUG列表"""
    bugs = Bug.objects.all().order_by('-created_at')

    # 过滤
    project_id = request.GET.get('project')
    if project_id:
        bugs = bugs.filter(project_id=project_id)

    status = request.GET.get('status')
    if status:
        bugs = bugs.filter(status=status)

    bug_type = request.GET.get('type')
    if bug_type:
        bugs = bugs.filter(bug_type=bug_type)

    # 分页
    paginator = Paginator(bugs, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取过滤选项
    projects = Project.objects.filter(is_active=True)

    context = {
        'page_obj': page_obj,
        'projects': projects,
        'current_project': project_id,
        'current_status': status,
        'current_type': bug_type,
        'status_choices': Bug.STATUS_CHOICES,
        'type_choices': Bug.BUG_TYPES,
    }

    return render(request, 'dashboard/bug_list.html', context)


def bug_detail(request, bug_id):
    """BUG详情"""
    bug = get_object_or_404(Bug, id=bug_id)

    context = {
        'bug': bug,
    }

    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        # AJAX请求，返回模态框内容
        return render(request, 'dashboard/bug_detail_modal.html', context)
    else:
        # 普通请求，返回完整页面
        return render(request, 'dashboard/bug_detail.html', context)


def create_bug(request):
    """创建BUG - 允许所有用户访问"""
    if request.method == 'GET':
        # 返回创建页面
        projects = Project.objects.filter(is_active=True)
        return render(request, 'dashboard/create_bug.html', {'projects': projects})

    elif request.method == 'POST':
        try:
            # 获取表单数据
            project_id = request.POST.get('project_id')
            bug_id = request.POST.get('bug_id')
            title = request.POST.get('title')
            description = request.POST.get('description')
            bug_type = request.POST.get('bug_type')
            severity = request.POST.get('severity')
            verification_phase = request.POST.get('verification_phase')
            discovered_platform = request.POST.get('discovered_platform', '')
            discovered_testcase = request.POST.get('discovered_testcase', '')

            # 验证必填字段
            if not all([project_id, bug_id, title, description, bug_type, severity, verification_phase]):
                return JsonResponse({'success': False, 'message': '请填写所有必填字段'})

            # 检查BUG ID是否已存在
            if Bug.objects.filter(bug_id=bug_id).exists():
                return JsonResponse({'success': False, 'message': f'BUG ID {bug_id} 已存在'})

            project = get_object_or_404(Project, id=project_id)

            # 创建默认用户（如果没有登录用户）
            from django.contrib.auth.models import User
            submitter = User.objects.first()  # 使用第一个用户作为提交者

            # 创建BUG
            bug = Bug.objects.create(
                project=project,
                bug_id=bug_id,
                title=title,
                description=description,
                bug_type=bug_type,
                severity=severity,
                verification_phase=verification_phase,
                discovered_platform=discovered_platform,
                discovered_testcase=discovered_testcase,
                submitter=submitter,
                status='open'
            )

            return JsonResponse({
                'success': True,
                'message': f'BUG {bug_id} 创建成功',
                'bug_id': bug.id
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'创建失败: {str(e)}'})

    else:
        return JsonResponse({'success': False, 'message': '不支持的请求方法'})


@csrf_exempt
def update_testcase_status(request):
    """更新测试用例状态的API接口"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})

    try:
        # 支持两种数据格式：JSON和表单数据
        if request.content_type == 'application/json':
            data = json.loads(request.body)
            case_name = data.get('case_name')
            command = data.get('command')
            result = data.get('result')  # 'pass' or 'fail'
            testcase_id = data.get('testcase_id')
            status = data.get('status')
        else:
            # 表单数据（来自前端页面）
            testcase_id = request.POST.get('testcase_id')
            status = request.POST.get('status')
            case_name = None
            command = None
            result = status

        if testcase_id and status:
            # 直接通过ID更新状态
            testcase = get_object_or_404(TestCase, id=testcase_id)

            # 更新子系统状态（默认更新子系统级状态）
            testcase.subsys_status = status
            testcase.save()

            return JsonResponse({'success': True, 'message': '状态更新成功'})

        elif case_name and command and result:
            # 通过用例名称和命令更新状态（RunSim集成）
            # 解析命令参数
            params = parse_command_params(command)

            # 查找测试用例
            testcase = TestCase.objects.filter(testcase_name=case_name).first()
            if not testcase:
                return JsonResponse({'success': False, 'message': f'未找到用例: {case_name}'})

            # 确定要更新的状态列
            status_field = determine_status_field(params)

            # 更新状态
            if result == 'start':
                # 开始执行
                setattr(testcase, status_field, 'on_going')
                testcase.start_time = timezone.now()
            elif result == 'pass':
                # 执行通过
                setattr(testcase, status_field, 'pass')
                testcase.end_time = timezone.now()
                if testcase.start_time:
                    testcase.actual_time = testcase.end_time - testcase.start_time
            elif result == 'fail':
                # 执行失败
                setattr(testcase, status_field, 'fail')
                # 失败时不更新结束时间

            testcase.save()

            # 创建执行记录
            ExecutionRecord.objects.create(
                testcase=testcase,
                command=command,
                case_param=case_name,
                base_param=params.get('base', ''),
                block_param=params.get('block', ''),
                post_param=params.get('post', ''),
                result=result if result != 'start' else 'running',
                start_time=testcase.start_time or timezone.now(),
                end_time=testcase.end_time if result == 'pass' else None,
                executor_id=1  # 默认用户ID，实际应该从请求中获取
            )

            return JsonResponse({'success': True, 'message': '状态更新成功'})

        else:
            return JsonResponse({'success': False, 'message': '缺少必要参数'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})


def parse_command_params(command):
    """解析命令参数"""
    params = {}
    parts = command.split()

    for i, part in enumerate(parts):
        if part == '-case' and i + 1 < len(parts):
            params['case'] = parts[i + 1]
        elif part == '-base' and i + 1 < len(parts):
            params['base'] = parts[i + 1]
        elif part == '-block' and i + 1 < len(parts):
            params['block'] = parts[i + 1]
        elif part == '-post' and i + 1 < len(parts):
            params['post'] = parts[i + 1]

    return params


def determine_status_field(params):
    """根据参数确定要更新的状态字段"""
    post_param = params.get('post', '')
    base_param = params.get('base', '')
    block_param = params.get('block', '')

    if not post_param:
        # 非后仿
        if base_param == 'top' or 'top' in block_param:
            return 'top_status'
        else:
            return 'subsys_status'
    else:
        # 后仿
        if base_param == 'top' or 'top' in block_param:
            return 'post_top_status'
        else:
            return 'post_subsys_status'


def execution_stats(request):
    """执行统计API"""
    # 获取最近7天的执行统计
    end_date = timezone.now().date()
    start_date = end_date - timedelta(days=6)

    stats = []
    for i in range(7):
        date = start_date + timedelta(days=i)
        executions = ExecutionRecord.objects.filter(
            start_time__date=date
        )

        stats.append({
            'date': date.strftime('%m-%d'),
            'total': executions.count(),
            'pass': executions.filter(result='pass').count(),
            'fail': executions.filter(result='fail').count(),
        })

    return JsonResponse({'stats': stats})


def bug_stats(request):
    """BUG趋势统计API - 增强版"""
    try:
        from datetime import datetime, timedelta
        from django.db.models import Count, Q

        # 获取参数
        project_id = request.GET.get('project')
        weeks = int(request.GET.get('weeks', 8))  # 默认显示8周

        # 计算时间范围
        end_date = timezone.now().date()
        start_date = end_date - timedelta(weeks=weeks)

        # 基础查询
        bugs = Bug.objects.all()
        if project_id:
            bugs = bugs.filter(project_id=project_id)

        # 按周统计
        weekly_stats = []
        cumulative_created = 0
        cumulative_resolved = 0

        for week_offset in range(weeks):
            week_start = start_date + timedelta(weeks=week_offset)
            week_end = week_start + timedelta(days=6)

            # 该周新增的BUG数量
            weekly_created = bugs.filter(
                created_at__date__gte=week_start,
                created_at__date__lte=week_end
            ).count()

            # 该周解决的BUG数量
            weekly_resolved = bugs.filter(
                resolved_at__date__gte=week_start,
                resolved_at__date__lte=week_end
            ).count()

            cumulative_created += weekly_created
            cumulative_resolved += weekly_resolved

            weekly_stats.append({
                'week': week_start.strftime('%Y-%m-%d'),
                'week_label': f"{week_start.strftime('%m/%d')}-{week_end.strftime('%m/%d')}",
                'created': weekly_created,
                'resolved': weekly_resolved,
                'cumulative_created': cumulative_created,
                'cumulative_resolved': cumulative_resolved
            })

        # 按日统计（最近30天）
        daily_stats = []
        daily_cumulative_created = 0
        daily_cumulative_resolved = 0

        for day_offset in range(30):
            current_date = end_date - timedelta(days=29-day_offset)

            # 该日新增的BUG数量
            daily_created = bugs.filter(
                created_at__date=current_date
            ).count()

            # 该日解决的BUG数量
            daily_resolved = bugs.filter(
                resolved_at__date=current_date
            ).count()

            daily_cumulative_created += daily_created
            daily_cumulative_resolved += daily_resolved

            daily_stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'date_label': current_date.strftime('%m/%d'),
                'created': daily_created,
                'resolved': daily_resolved,
                'cumulative_created': daily_cumulative_created,
                'cumulative_resolved': daily_cumulative_resolved
            })

        # 总体统计
        total_bugs = bugs.count()
        total_resolved = bugs.filter(status='resolved').count()
        total_open = bugs.filter(status__in=['open', 'in_progress']).count()
        resolution_rate = (total_resolved / total_bugs * 100) if total_bugs > 0 else 0

        # 按严重程度统计
        severity_stats = {
            'critical': bugs.filter(severity='critical').count(),
            'high': bugs.filter(severity='high').count(),
            'medium': bugs.filter(severity='medium').count(),
            'low': bugs.filter(severity='low').count(),
        }

        # 按类型统计
        type_stats = {
            'functional': bugs.filter(bug_type='functional').count(),
            'performance': bugs.filter(bug_type='performance').count(),
            'interface': bugs.filter(bug_type='interface').count(),
            'compatibility': bugs.filter(bug_type='compatibility').count(),
            'security': bugs.filter(bug_type='security').count(),
            'other': bugs.filter(bug_type='other').count(),
        }

        # 按状态统计
        status_stats = {
            'open': bugs.filter(status='open').count(),
            'in_progress': bugs.filter(status='in_progress').count(),
            'resolved': bugs.filter(status='resolved').count(),
            'closed': bugs.filter(status='closed').count(),
            'rejected': bugs.filter(status='rejected').count(),
        }

        return JsonResponse({
            'success': True,
            'data': {
                'weekly_stats': weekly_stats,
                'daily_stats': daily_stats,
                'summary': {
                    'total_bugs': total_bugs,
                    'total_resolved': total_resolved,
                    'total_open': total_open,
                    'resolution_rate': round(resolution_rate, 2)
                },
                'severity_stats': severity_stats,
                'type_stats': type_stats,
                'status_stats': status_stats
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'获取BUG趋势统计失败: {str(e)}'})


@csrf_exempt
def runsim_execution_start(request):
    """RunSim执行开始API"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})

    try:
        data = json.loads(request.body)
        command = data.get('command', '')
        case_name = data.get('case_name', '')
        run_dir = data.get('run_dir', '')

        if not case_name:
            return JsonResponse({'success': False, 'message': '缺少用例名称'})

        # 导入集成模块
        from .integration import runsim_integration

        # 调用集成接口
        runsim_integration.on_simulation_start(command, case_name, run_dir)

        return JsonResponse({
            'success': True,
            'message': f'用例 {case_name} 执行开始记录成功'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'处理失败: {str(e)}'})


@csrf_exempt
def runsim_execution_complete(request):
    """RunSim执行完成API"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})

    try:
        data = json.loads(request.body)
        case_name = data.get('case_name', '')
        run_dir = data.get('run_dir', '')
        success = data.get('success')  # True/False 或 None表示自动检测

        if not case_name:
            return JsonResponse({'success': False, 'message': '缺少用例名称'})

        # 导入集成模块
        from .integration import runsim_integration

        # 调用集成接口
        runsim_integration.on_simulation_complete(case_name, run_dir, success)

        result_text = "通过" if success else "失败" if success is False else "自动检测"
        return JsonResponse({
            'success': True,
            'message': f'用例 {case_name} 执行完成，结果: {result_text}'
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'处理失败: {str(e)}'})


def runsim_integration_status(request):
    """获取RunSim集成状态"""
    try:
        from .integration import runsim_integration
        status = runsim_integration.get_status()

        # 添加额外的统计信息
        recent_executions = ExecutionRecord.objects.filter(
            start_time__gte=timezone.now() - timedelta(hours=24)
        ).count()

        active_cases = ExecutionRecord.objects.filter(result='running').count()

        status.update({
            'recent_executions_24h': recent_executions,
            'active_cases': active_cases,
            'database_connected': True,
        })

        return JsonResponse(status)

    except Exception as e:
        return JsonResponse({
            'error': str(e),
            'monitoring': False,
            'database_connected': False,
        })


def export_testcases(request):
    """导出测试用例 - 使用标准TestPlan格式"""
    try:
        format_type = request.GET.get('format', 'excel')
        project_id = request.GET.get('project')
        testplan_id = request.GET.get('testplan')

        if format_type == 'excel':
            # 如果指定了测试计划，导出该测试计划
            if testplan_id:
                testplan = get_object_or_404(TestPlan, id=testplan_id)
                from .utils import TestPlanExporter
                exporter = TestPlanExporter(testplan)
                result = exporter.export_to_excel()

                if result['success']:
                    # 返回文件下载
                    with open(result['file_path'], 'rb') as f:
                        response = HttpResponse(
                            f.read(),
                            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        )
                        response['Content-Disposition'] = f'attachment; filename="{result["filename"]}"'
                        return response
                else:
                    return JsonResponse({'success': False, 'message': result['message']})

            # 如果没有指定测试计划，导出项目下所有用例（简化格式）
            else:
                testcases = TestCase.objects.all()
                if project_id:
                    testcases = testcases.filter(testplan__project_id=project_id)

                import pandas as pd
                from django.http import HttpResponse

                # 按照21列标准格式导出
                data = []
                for tc in testcases:
                    # 状态映射
                    status_mapping = {
                        'not_started': 'N/A',
                        'on_going': 'On-Going',
                        'pass': 'PASS',
                        'fail': 'FAIL',
                        'skip': 'Skip'
                    }

                    data.append({
                        'Test Category': tc.test_category,  # A列
                        'Items': tc.items,  # B列
                        'Test Areas': tc.test_areas,  # C列
                        'Function points': tc.function_points,  # D列
                        'Test Scope': tc.test_scope,  # E列
                        'Check Point': tc.check_point,  # F列
                        'Cover': tc.cover,  # G列
                        'TestCase Name': tc.testcase_name,  # H列
                        'Start Time': tc.start_time.strftime('%Y/%m/%d') if tc.start_time else '',  # I列
                        'End Time': tc.end_time.strftime('%Y/%m/%d') if tc.end_time else '',  # J列
                        'Actual Time': str(tc.actual_time) if tc.actual_time else '',  # K列
                        'Owner': tc.owner,  # L列
                        'Subsys Phase': tc.subsys_phase,  # M列
                        'Subsys Status': status_mapping.get(tc.subsys_status, tc.subsys_status),  # N列
                        'TOP Phase': tc.top_phase,  # O列
                        'TOP Status': status_mapping.get(tc.top_status, tc.top_status),  # P列
                        'POST_Subsys Phase': tc.post_subsys_phase,  # Q列
                        'POST_Subsys Status': status_mapping.get(tc.post_subsys_status, tc.post_subsys_status),  # R列
                        'POST_TOP Phase': tc.post_top_phase,  # S列
                        'POST_TOP Status': status_mapping.get(tc.post_top_status, tc.post_top_status),  # T列
                        'Note': tc.note,  # U列
                    })

                df = pd.DataFrame(data)

                response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = f'attachment; filename="testcases_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

                df.to_excel(response, index=False, engine='openpyxl')
                return response

        else:
            return JsonResponse({'success': False, 'message': '不支持的导出格式'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'导出失败: {str(e)}'})


def export_bugs(request):
    """导出BUG列表"""
    try:
        format_type = request.GET.get('format', 'excel')
        project_id = request.GET.get('project')

        # 获取BUG列表
        bugs = Bug.objects.all()
        if project_id:
            bugs = bugs.filter(project_id=project_id)

        if format_type == 'excel':
            import pandas as pd
            from django.http import HttpResponse

            data = []
            for bug in bugs:
                data.append({
                    'BUG ID': bug.bug_id,
                    '标题': bug.title,
                    '项目': bug.project.name,
                    '类型': bug.get_bug_type_display(),
                    '严重程度': bug.get_severity_display(),
                    '状态': bug.get_status_display(),
                    '提交者': bug.submitter.username,
                    '验证人': bug.assignee.username if bug.assignee else '',
                    '验证阶段': bug.get_verification_phase_display(),
                    '发现平台': bug.discovered_platform,
                    '发现用例': bug.discovered_testcase,
                    '创建时间': bug.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    '解决时间': bug.resolved_at.strftime('%Y-%m-%d %H:%M:%S') if bug.resolved_at else '',
                    '描述': bug.description,
                })

            df = pd.DataFrame(data)

            response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="bugs_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

            df.to_excel(response, index=False, engine='openpyxl')
            return response

        else:
            return JsonResponse({'success': False, 'message': '不支持的导出格式'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'导出失败: {str(e)}'})


def upload_testplan(request):
    """上传测试计划文件 - 允许所有用户访问"""
    if request.method == 'GET':
        # 返回上传页面
        projects = Project.objects.filter(is_active=True)
        return render(request, 'dashboard/upload_testplan.html', {'projects': projects})

    elif request.method == 'POST':
        try:
            project_id = request.POST.get('project_id')
            testplan_name = request.POST.get('testplan_name')
            excel_file = request.FILES.get('excel_file')

            if not all([project_id, testplan_name, excel_file]):
                return JsonResponse({'success': False, 'message': '缺少必要参数'})

            project = get_object_or_404(Project, id=project_id)

            # 创建测试计划
            testplan = TestPlan.objects.create(
                project=project,
                name=testplan_name,
                excel_file=excel_file
            )

            # 导入测试用例
            from .utils import TestPlanImporter
            importer = TestPlanImporter(testplan)
            result = importer.import_testcases()

            if result['success']:
                return JsonResponse({
                    'success': True,
                    'message': f'测试计划上传成功，{result["message"]}',
                    'testplan_id': testplan.id
                })
            else:
                # 如果导入失败，删除测试计划
                testplan.delete()
                return JsonResponse({'success': False, 'message': result['message']})

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'上传失败: {str(e)}'})

    else:
        return JsonResponse({'success': False, 'message': '不支持的请求方法'})


def edit_bug(request, bug_id):
    """编辑BUG - 允许所有用户访问"""
    bug = get_object_or_404(Bug, id=bug_id)

    if request.method == 'GET':
        # 返回编辑页面
        projects = Project.objects.filter(is_active=True)
        context = {'bug': bug, 'projects': projects}
        return render(request, 'dashboard/edit_bug.html', context)

    elif request.method == 'POST':
        try:
            # 更新BUG信息
            bug.title = request.POST.get('title', bug.title)
            bug.description = request.POST.get('description', bug.description)
            bug.bug_type = request.POST.get('bug_type', bug.bug_type)
            bug.severity = request.POST.get('severity', bug.severity)
            bug.verification_phase = request.POST.get('verification_phase', bug.verification_phase)
            bug.discovered_platform = request.POST.get('discovered_platform', bug.discovered_platform)
            bug.discovered_testcase = request.POST.get('discovered_testcase', bug.discovered_testcase)
            bug.status = request.POST.get('status', bug.status)

            # 如果状态变为已解决，记录解决时间
            if bug.status == 'resolved' and not bug.resolved_at:
                bug.resolved_at = timezone.now()

            bug.save()

            return JsonResponse({
                'success': True,
                'message': 'BUG更新成功'
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})

    else:
        return JsonResponse({'success': False, 'message': '不支持的请求方法'})


@csrf_exempt
def update_bug_status(request):
    """更新BUG状态API"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '只支持POST请求'})

    try:
        bug_id = request.POST.get('bug_id')
        status = request.POST.get('status')

        if not all([bug_id, status]):
            return JsonResponse({'success': False, 'message': '缺少必要参数'})

        bug = get_object_or_404(Bug, id=bug_id)
        bug.status = status

        # 如果状态变为已解决，记录解决时间
        if status == 'resolved' and not bug.resolved_at:
            bug.resolved_at = timezone.now()

        bug.save()

        return JsonResponse({'success': True, 'message': 'BUG状态更新成功'})

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})


def api_testplans(request):
    """获取测试计划列表API"""
    try:
        testplans = TestPlan.objects.all().order_by('-uploaded_at')

        # 过滤
        project_id = request.GET.get('project')
        if project_id:
            testplans = testplans.filter(project_id=project_id)

        data = []
        for tp in testplans:
            data.append({
                'id': tp.id,
                'name': tp.name,
                'project_name': tp.project.name,
                'subsystem_name': tp.subsystem_name,
                'uploaded_at': tp.uploaded_at.strftime('%Y-%m-%d %H:%M:%S'),
                'testcase_count': TestCase.objects.filter(testplan=tp).count()
            })

        return JsonResponse({
            'success': True,
            'testplans': data
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'获取失败: {str(e)}'})


def testcase_trend_stats(request):
    """用例趋势统计API"""
    try:
        from datetime import datetime, timedelta
        from django.db.models import Count, Q
        from django.db.models.functions import TruncWeek, TruncDate

        # 获取参数
        project_id = request.GET.get('project')
        weeks = int(request.GET.get('weeks', 8))  # 默认显示8周

        # 计算时间范围
        end_date = timezone.now().date()
        start_date = end_date - timedelta(weeks=weeks)

        # 基础查询
        testcases = TestCase.objects.all()
        if project_id:
            testcases = testcases.filter(testplan__project_id=project_id)

        # 按周统计新增通过的用例
        weekly_pass_stats = []
        cumulative_pass = 0

        # 获取所有通过的用例（任何状态列为pass的用例）
        passed_cases = testcases.filter(
            Q(subsys_status='pass') |
            Q(top_status='pass') |
            Q(post_subsys_status='pass') |
            Q(post_top_status='pass')
        ).distinct()

        # 按周统计
        for week_offset in range(weeks):
            week_start = start_date + timedelta(weeks=week_offset)
            week_end = week_start + timedelta(days=6)

            # 该周新增通过的用例数量（基于end_time）
            weekly_new_pass = passed_cases.filter(
                end_time__date__gte=week_start,
                end_time__date__lte=week_end
            ).count()

            cumulative_pass += weekly_new_pass

            weekly_pass_stats.append({
                'week': week_start.strftime('%Y-%m-%d'),
                'week_label': f"{week_start.strftime('%m/%d')}-{week_end.strftime('%m/%d')}",
                'new_pass': weekly_new_pass,
                'cumulative_pass': cumulative_pass
            })

        # 按日统计（最近30天）
        daily_stats = []
        daily_cumulative = 0

        for day_offset in range(30):
            current_date = end_date - timedelta(days=29-day_offset)

            # 该日新增通过的用例数量
            daily_new_pass = passed_cases.filter(
                end_time__date=current_date
            ).count()

            daily_cumulative += daily_new_pass

            daily_stats.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'date_label': current_date.strftime('%m/%d'),
                'new_pass': daily_new_pass,
                'cumulative_pass': daily_cumulative
            })

        # 总体统计
        total_cases = testcases.count()
        total_passed = passed_cases.count()
        pass_rate = (total_passed / total_cases * 100) if total_cases > 0 else 0

        # 各状态列的统计
        status_stats = {
            'subsys': {
                'total': testcases.count(),
                'pass': testcases.filter(subsys_status='pass').count(),
                'fail': testcases.filter(subsys_status='fail').count(),
                'on_going': testcases.filter(subsys_status='on_going').count(),
                'not_started': testcases.filter(subsys_status='not_started').count(),
            },
            'top': {
                'total': testcases.count(),
                'pass': testcases.filter(top_status='pass').count(),
                'fail': testcases.filter(top_status='fail').count(),
                'on_going': testcases.filter(top_status='on_going').count(),
                'not_started': testcases.filter(top_status='not_started').count(),
            },
            'post_subsys': {
                'total': testcases.count(),
                'pass': testcases.filter(post_subsys_status='pass').count(),
                'fail': testcases.filter(post_subsys_status='fail').count(),
                'on_going': testcases.filter(post_subsys_status='on_going').count(),
                'not_started': testcases.filter(post_subsys_status='not_started').count(),
            },
            'post_top': {
                'total': testcases.count(),
                'pass': testcases.filter(post_top_status='pass').count(),
                'fail': testcases.filter(post_top_status='fail').count(),
                'on_going': testcases.filter(post_top_status='on_going').count(),
                'not_started': testcases.filter(post_top_status='not_started').count(),
            }
        }

        return JsonResponse({
            'success': True,
            'data': {
                'weekly_stats': weekly_pass_stats,
                'daily_stats': daily_stats,
                'summary': {
                    'total_cases': total_cases,
                    'total_passed': total_passed,
                    'pass_rate': round(pass_rate, 2)
                },
                'status_stats': status_stats
            }
        })

    except Exception as e:
        return JsonResponse({'success': False, 'message': f'获取趋势统计失败: {str(e)}'})


def create_project(request):
    """创建项目 - 允许所有用户访问"""
    if request.method == 'GET':
        # 返回创建页面
        from django.contrib.auth.models import User
        users = User.objects.filter(is_active=True)
        return render(request, 'dashboard/create_project.html', {'users': users})

    elif request.method == 'POST':
        try:
            # 创建项目
            name = request.POST.get('name')
            description = request.POST.get('description', '')
            owner_id = request.POST.get('owner_id')
            current_soc_phase = request.POST.get('current_soc_phase', 'kickoff')
            current_verification_phase = request.POST.get('current_verification_phase', 'dvr1')

            if not all([name, owner_id]):
                return JsonResponse({'success': False, 'message': '项目名称和负责人为必填项'})

            # 检查项目名称是否已存在
            if Project.objects.filter(name=name).exists():
                return JsonResponse({'success': False, 'message': '项目名称已存在'})

            from django.contrib.auth.models import User
            owner = get_object_or_404(User, id=owner_id)

            project = Project.objects.create(
                name=name,
                description=description,
                owner=owner,
                current_soc_phase=current_soc_phase,
                current_verification_phase=current_verification_phase
            )

            return JsonResponse({
                'success': True,
                'message': '项目创建成功',
                'project_id': project.id
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'创建失败: {str(e)}'})

    else:
        return JsonResponse({'success': False, 'message': '不支持的请求方法'})


def edit_project(request, project_id):
    """编辑项目 - 允许所有用户访问"""
    project = get_object_or_404(Project, id=project_id)

    if request.method == 'GET':
        # 返回编辑页面
        from django.contrib.auth.models import User
        users = User.objects.filter(is_active=True)
        context = {'project': project, 'users': users}
        return render(request, 'dashboard/edit_project.html', context)

    elif request.method == 'POST':
        try:
            # 更新项目信息
            project.name = request.POST.get('name', project.name)
            project.description = request.POST.get('description', project.description)

            owner_id = request.POST.get('owner_id')
            if owner_id:
                from django.contrib.auth.models import User
                project.owner = get_object_or_404(User, id=owner_id)

            project.current_soc_phase = request.POST.get('current_soc_phase', project.current_soc_phase)
            project.current_verification_phase = request.POST.get('current_verification_phase', project.current_verification_phase)

            # 检查项目名称是否与其他项目冲突
            if Project.objects.filter(name=project.name).exclude(id=project.id).exists():
                return JsonResponse({'success': False, 'message': '项目名称已存在'})

            project.save()

            return JsonResponse({
                'success': True,
                'message': '项目更新成功'
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'更新失败: {str(e)}'})

    else:
        return JsonResponse({'success': False, 'message': '不支持的请求方法'})
