"""
RunSim GUI与仪表盘系统集成模块

这个模块提供了将现有RunSim GUI与新的仪表盘系统集成的接口。
可以在现有的RunSim GUI代码中导入并使用这些函数。
"""

import requests
import json
import os
import threading
import time
from datetime import datetime
from PyQt5.QtWidgets import QMessageBox

class DashboardIntegration:
    """仪表盘集成类"""
    
    def __init__(self, dashboard_url="http://localhost:8000"):
        self.dashboard_url = dashboard_url
        self.enabled = True
        self.timeout = 5  # 请求超时时间
        
    def is_dashboard_available(self):
        """检查仪表盘是否可用"""
        try:
            response = requests.get(
                f"{self.dashboard_url}/api/runsim/status/",
                timeout=self.timeout
            )
            return response.status_code == 200
        except:
            return False
    
    def notify_execution_start(self, command, case_name, run_dir=None):
        """通知仿真执行开始"""
        if not self.enabled:
            return
            
        try:
            data = {
                'command': command,
                'case_name': case_name,
                'run_dir': run_dir or '',
                'timestamp': datetime.now().isoformat()
            }
            
            response = requests.post(
                f"{self.dashboard_url}/api/runsim/execution/start/",
                json=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 仪表盘已记录执行开始: {case_name}")
                else:
                    print(f"⚠ 仪表盘记录失败: {result.get('message', '未知错误')}")
            else:
                print(f"⚠ 仪表盘通信失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"⚠ 通知仪表盘执行开始时出错: {e}")
    
    def notify_execution_complete(self, case_name, run_dir=None, success=None):
        """通知仿真执行完成"""
        if not self.enabled:
            return
            
        try:
            data = {
                'case_name': case_name,
                'run_dir': run_dir or '',
                'success': success,
                'timestamp': datetime.now().isoformat()
            }
            
            response = requests.post(
                f"{self.dashboard_url}/api/runsim/execution/complete/",
                json=data,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✓ 仪表盘已记录执行完成: {case_name}")
                else:
                    print(f"⚠ 仪表盘记录失败: {result.get('message', '未知错误')}")
            else:
                print(f"⚠ 仪表盘通信失败: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"⚠ 通知仪表盘执行完成时出错: {e}")
    
    def get_dashboard_status(self):
        """获取仪表盘状态"""
        try:
            response = requests.get(
                f"{self.dashboard_url}/api/runsim/status/",
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            return {'error': str(e)}
    
    def enable(self):
        """启用仪表盘集成"""
        self.enabled = True
        print("✓ 仪表盘集成已启用")
    
    def disable(self):
        """禁用仪表盘集成"""
        self.enabled = False
        print("✓ 仪表盘集成已禁用")


# 全局集成实例
dashboard_integration = DashboardIntegration()


def integrate_with_execution_controller(execution_controller):
    """
    与现有ExecutionController集成的函数
    
    这个函数展示了如何在现有的ExecutionController中添加仪表盘集成
    """
    
    # 保存原始的execute_command方法
    original_execute_command = execution_controller.execute_command
    
    def enhanced_execute_command(command, case_name):
        """增强的execute_command方法"""
        
        # 通知仪表盘执行开始
        run_dir = execution_controller.config_model.get_value('rundir', '')
        dashboard_integration.notify_execution_start(command, case_name, run_dir)
        
        # 调用原始方法
        result = original_execute_command(command, case_name)
        
        # 这里可以根据执行结果通知仪表盘
        # 实际实现时需要根据ExecutionController的具体实现来调整
        
        return result
    
    # 替换原始方法
    execution_controller.execute_command = enhanced_execute_command
    
    print("✓ ExecutionController已集成仪表盘功能")


def create_dashboard_menu_action(main_window):
    """
    为主窗口创建仪表盘菜单项
    
    这个函数展示了如何在现有的主窗口中添加仪表盘相关的菜单项
    """
    from PyQt5.QtWidgets import QAction
    from PyQt5.QtCore import QUrl
    from PyQt5.QtGui import QDesktopServices
    
    # 创建仪表盘菜单项
    dashboard_action = QAction("打开仪表盘", main_window)
    dashboard_action.triggered.connect(lambda: QDesktopServices.openUrl(QUrl("http://localhost:8000")))
    
    # 创建仪表盘状态菜单项
    status_action = QAction("仪表盘状态", main_window)
    status_action.triggered.connect(show_dashboard_status)
    
    # 创建集成设置菜单项
    settings_action = QAction("集成设置", main_window)
    settings_action.triggered.connect(show_integration_settings)
    
    # 添加到工具菜单
    if hasattr(main_window, 'tools_menu'):
        main_window.tools_menu.addSeparator()
        main_window.tools_menu.addAction(dashboard_action)
        main_window.tools_menu.addAction(status_action)
        main_window.tools_menu.addAction(settings_action)
    
    print("✓ 仪表盘菜单项已添加")


def show_dashboard_status():
    """显示仪表盘状态对话框"""
    from PyQt5.QtWidgets import QMessageBox
    
    status = dashboard_integration.get_dashboard_status()
    
    if 'error' in status:
        message = f"仪表盘连接失败: {status['error']}"
        QMessageBox.warning(None, "仪表盘状态", message)
    else:
        message = f"""仪表盘状态:
监控状态: {'运行中' if status.get('monitoring') else '已停止'}
活跃监控器: {status.get('active_watchers', 0)}
24小时内执行: {status.get('recent_executions_24h', 0)}
当前活跃用例: {status.get('active_cases', 0)}
数据库连接: {'正常' if status.get('database_connected') else '异常'}
最后检查: {status.get('last_check', '未知')}"""
        
        QMessageBox.information(None, "仪表盘状态", message)


def show_integration_settings():
    """显示集成设置对话框"""
    from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QCheckBox
    
    dialog = QDialog()
    dialog.setWindowTitle("仪表盘集成设置")
    dialog.setFixedSize(400, 200)
    
    layout = QVBoxLayout()
    
    # 仪表盘URL设置
    url_layout = QHBoxLayout()
    url_layout.addWidget(QLabel("仪表盘URL:"))
    url_input = QLineEdit(dashboard_integration.dashboard_url)
    url_layout.addWidget(url_input)
    layout.addLayout(url_layout)
    
    # 启用/禁用集成
    enable_checkbox = QCheckBox("启用仪表盘集成")
    enable_checkbox.setChecked(dashboard_integration.enabled)
    layout.addWidget(enable_checkbox)
    
    # 按钮
    button_layout = QHBoxLayout()
    
    test_button = QPushButton("测试连接")
    def test_connection():
        dashboard_integration.dashboard_url = url_input.text()
        if dashboard_integration.is_dashboard_available():
            QMessageBox.information(dialog, "测试结果", "连接成功！")
        else:
            QMessageBox.warning(dialog, "测试结果", "连接失败！")
    test_button.clicked.connect(test_connection)
    
    save_button = QPushButton("保存")
    def save_settings():
        dashboard_integration.dashboard_url = url_input.text()
        if enable_checkbox.isChecked():
            dashboard_integration.enable()
        else:
            dashboard_integration.disable()
        dialog.accept()
    save_button.clicked.connect(save_settings)
    
    cancel_button = QPushButton("取消")
    cancel_button.clicked.connect(dialog.reject)
    
    button_layout.addWidget(test_button)
    button_layout.addWidget(save_button)
    button_layout.addWidget(cancel_button)
    layout.addLayout(button_layout)
    
    dialog.setLayout(layout)
    dialog.exec_()


def setup_automatic_result_detection(execution_controller):
    """
    设置自动结果检测

    这个函数展示了如何监控执行结果并自动通知仪表盘
    """

    def monitor_execution_result(case_name, run_dir, command):
        """监控执行结果的线程函数"""
        log_file = os.path.join(run_dir, 'irun_sim.log')

        print(f"开始监控用例: {case_name}")
        print(f"日志文件: {log_file}")

        # 通知仪表盘执行开始
        dashboard_integration.notify_execution_start(command, case_name, run_dir)

        # 等待日志文件出现
        timeout = 300  # 5分钟超时
        start_time = time.time()

        while not os.path.exists(log_file) and (time.time() - start_time) < timeout:
            time.sleep(1)

        if not os.path.exists(log_file):
            print(f"⚠ 日志文件未找到: {log_file}")
            # 通知仪表盘执行失败
            dashboard_integration.notify_execution_complete(case_name, run_dir, False)
            return

        print(f"✓ 找到日志文件，开始监控: {log_file}")

        # 监控日志文件变化
        last_size = 0
        no_change_count = 0
        max_no_change = 60  # 60秒无变化则认为执行完成

        while no_change_count < max_no_change:
            try:
                current_size = os.path.getsize(log_file)
                if current_size > last_size:
                    last_size = current_size
                    no_change_count = 0

                    # 实时检查是否已经完成
                    if check_simulation_completion(log_file):
                        print(f"✓ 检测到仿真完成标志")
                        break
                else:
                    no_change_count += 1

                time.sleep(1)

            except Exception as e:
                print(f"监控日志文件时出错: {e}")
                break

        # 检查执行结果
        success = check_simulation_result(log_file)
        result_text = "通过" if success else "失败"
        print(f"✓ 用例 {case_name} 执行完成，结果: {result_text}")

        # 通知仪表盘
        dashboard_integration.notify_execution_complete(case_name, run_dir, success)

    def check_simulation_completion(log_file):
        """检查仿真是否完成"""
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                last_lines = lines[-20:] if len(lines) > 20 else lines

                for line in last_lines:
                    # 检查常见的仿真完成标志
                    if any(keyword in line for keyword in [
                        'SPRD_PASSED', 'SPRD_FAILED', 'Simulation complete',
                        'ncsim: *E,', 'ncsim: *W,RNQUIE', 'exit'
                    ]):
                        return True

                return False
        except:
            return False

    def check_simulation_result(log_file):
        """检查仿真结果"""
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                last_lines = lines[-50:] if len(lines) > 50 else lines

                # 检查通过标志
                for line in last_lines:
                    if 'SPRD_PASSED' in line:
                        return True

                # 检查失败标志
                for line in last_lines:
                    if any(keyword in line for keyword in ['SPRD_FAILED', 'ncsim: *E,']):
                        return False

                # 如果没有明确的通过/失败标志，默认为失败
                return False
        except:
            return False

    # 保存原始方法并增强
    original_execute = execution_controller.execute_command

    def enhanced_execute(command, case_name):
        # 调用原始方法
        result = original_execute(command, case_name)

        # 获取运行目录
        run_dir = execution_controller.config_model.get_value('rundir', '')
        if run_dir:
            # 启动结果监控线程
            monitor_thread = threading.Thread(
                target=monitor_execution_result,
                args=(case_name, run_dir, command)
            )
            monitor_thread.daemon = True
            monitor_thread.start()
            print(f"✓ 启动监控线程: {case_name}")
        else:
            print(f"⚠ 未找到运行目录，跳过监控: {case_name}")

        return result

    execution_controller.execute_command = enhanced_execute
    print("✓ 自动结果检测已设置")


# 使用示例
def integrate_dashboard_with_runsim_gui(app_controller):
    """
    完整的集成示例
    
    这个函数展示了如何将仪表盘功能完全集成到现有的RunSim GUI中
    """
    
    print("开始集成仪表盘功能...")
    
    # 1. 检查仪表盘是否可用
    if dashboard_integration.is_dashboard_available():
        print("✓ 仪表盘服务可用")
    else:
        print("⚠ 仪表盘服务不可用，集成功能将被禁用")
        dashboard_integration.disable()
        return
    
    # 2. 集成执行控制器
    if hasattr(app_controller, 'execution_controller'):
        integrate_with_execution_controller(app_controller.execution_controller)
        setup_automatic_result_detection(app_controller.execution_controller)
    
    # 3. 添加菜单项
    if hasattr(app_controller, 'main_window'):
        create_dashboard_menu_action(app_controller.main_window)
    
    # 4. 启用集成
    dashboard_integration.enable()
    
    print("✅ 仪表盘集成完成！")
    print("现在可以通过菜单访问仪表盘功能")


if __name__ == "__main__":
    # 测试集成功能
    print("测试仪表盘集成...")
    
    if dashboard_integration.is_dashboard_available():
        print("✓ 仪表盘服务可用")
        
        # 测试通知功能
        dashboard_integration.notify_execution_start(
            "runsim -case test_case -base top",
            "test_case",
            "/tmp/test_run"
        )
        
        time.sleep(2)
        
        dashboard_integration.notify_execution_complete(
            "test_case",
            "/tmp/test_run",
            True
        )
        
        print("✓ 测试完成")
    else:
        print("⚠ 仪表盘服务不可用")
