"""
命令生成器模块
"""
import sys
import os

class CommandGenerator:
    """runsim 命令生成器"""

    # 添加类变量用于防止递归
    _recursion_depth = 0
    _max_recursion_depth = 10

    @classmethod
    def generate_command(cls, config, mode="normal", case_name=None, has_terminal=False):
        """
        生成 runsim 命令。

        Args:
            config (dict): 配置参数字典
            mode (str): 执行模式，例如 "normal", "R", "C" 等
            case_name (str, optional): 指定的用例名称，默认为 None
            has_terminal (bool): 是否有终端集成支持

        Returns:
            str: 生成的 runsim 命令字符串
        """
        # 防止无限递归
        cls._recursion_depth += 1
        if cls._recursion_depth > cls._max_recursion_depth:
            cls._recursion_depth = 0
            return "runsim [递归深度超过限制]"

        try:
            cmd = ["runsim"]  # 初始化命令列表，runsim 命令主体

            if not config.get("regr_file"):  # 如果没有选择回归列表文件，则使用基本参数和用例参数
                if config.get("base"):  # 如果 BASE 参数不为空
                    cmd += ["-base", config["base"]]  # 添加 -base 参数
                if config.get("block"):  # 如果 BLOCK 参数不为空
                    cmd += ["-block", config["block"]]  # 添加 -block 参数
                if case_name:  # 如果指定了用例名称
                    cmd += ["-case", case_name]  # 添加 -case 参数
            else:  # 如果选择了回归列表文件
                cmd += ["-regr", config["regr_file"]]  # 添加 -regr 参数，指定回归列表文件
                # 添加-fm参数
                if config.get("fm_checked", False):
                    cmd.append("-fm")
                # 添加-bp参数
                bp_server = config.get("bp_server", "").strip()
                if bp_server:
                    cmd += ["-bp", bp_server]

            # 添加 rundir 参数，指定工作目录
            rundir = config.get("rundir", "").strip()
            if rundir:
                cmd += ["-rundir", rundir]

            # 添加波形 dump 相关参数
            if config.get("fsdb", False):  # 如果勾选了 Dump FSDB 波形
                cmd.append("-fsdb")  # 添加 -fsdb 参数
                if config.get("fsdb_file"):  # 如果选择了 FSDB tcl 文件
                    cmd.append(config["fsdb_file"])  # 添加 FSDB tcl 文件路径
            if config.get("vwdb", False):  # 如果勾选了 Dump VWDB 波形
                cmd.append("-vwdb")  # 添加 -vwdb 参数
                if config.get("fsdb_file"):  # 如果选择了 FSDB tcl 文件 (复用 FSDB 的 TCL 文件)
                    cmd.append(config["fsdb_file"])  # 添加 FSDB tcl 文件路径
            if config.get("cl", False):  # 如果勾选了 Clean INCA_libs
                cmd.append("-cl")  # 添加 -cl 参数
            if config.get("dump_sva", False):  # 如果勾选了 Dump SVA 断言
                cmd.append("-dump_sva")  # 添加 -dump_sva 参数
            if config.get("cov", False):  # 如果勾选了 收集覆盖率
                cmd.append("-cov")  # 添加 -cov 参数
            if config.get("upf", False):  # 如果勾选了 UPF 仿真
                cmd.append("-upf")  # 添加 -upf 参数

            # 如果 Dump Memory 参数不为空
            dump_mem = config.get("dump_mem", "")
            if dump_mem:
                # 如果dump_mem包含多个选项，需要用引号包裹
                if " " in dump_mem:
                    cmd += ["-dump_mem", f'"{dump_mem}"']  # 添加 -dump_mem 参数，多个选项用引号包裹
                else:
                    cmd += ["-dump_mem", dump_mem]  # 添加 -dump_mem 参数

            # 如果 波形 Dump 起始时间 参数不为空
            wdd = config.get("wdd", "").strip()
            if wdd:
                cmd += ["-wdd", wdd]  # 添加 -wdd 参数

            # 如果 种子号 参数不为空
            seed = config.get("seed", "").strip()
            if seed:
                cmd += ["-seed", seed]  # 添加 -seed 参数

            # 如果 仿真参数 不为空
            simarg = config.get("simarg", "").strip()
            if simarg:
                cmd += ["-simarg", f'"{simarg}"']  # 添加 -simarg 参数，参数值用双引号包裹

            # 如果 配置定义 不为空
            cfg_def = config.get("cfg_def", "").strip()
            if cfg_def:
                cmd += ["-cfg_def", cfg_def]  # 添加 -cfg_def 参数

            # 如果 后仿参数 不为空
            post = config.get("post", "").strip()
            if post:
                cmd += ["-post", post]  # 添加 -post 参数

            if mode != "normal":  # 如果执行模式不是 normal
                cmd.append(f"-{mode}")  # 添加模式参数，例如 -R, -C

            # 添加其他选项，直接添加到命令末尾
            other_options = config.get("other_options", "").strip()
            if other_options:  # 如果 其他选项 不为空
                cmd.append(other_options)  # 添加其他选项

            # 针对终端集成,添加必要的环境设置
            prefix = ""
            if has_terminal and sys.platform != 'win32':
                # 添加工作目录切换
                rundir = config.get("rundir", "").strip()
                if rundir:
                    prefix = f"cd {rundir} && "

            # 构建完整命令
            final_cmd = prefix + " ".join(cmd)

            # 添加命令执行完成提示
            if has_terminal and sys.platform != 'win32':
                final_cmd += ' ; echo "\n[Command completed]"'

            return final_cmd
        finally:
            # 减少递归深度
            cls._recursion_depth -= 1
