# Generated by Django 5.2.2 on 2025-06-05 10:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dashboard', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Subsystem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='子系统名称')),
                ('description', models.TextField(blank=True, verbose_name='子系统描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.project', verbose_name='所属项目')),
            ],
            options={
                'verbose_name': '子系统',
                'verbose_name_plural': '子系统',
                'ordering': ['project', 'name'],
                'unique_together': {('project', 'name')},
            },
        ),
    ]
