from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QTableWidget, QTableWidgetItem, QMessageBox, QHeaderView,
                           QFileDialog, QLabel, QToolButton)
from PyQt5.QtCore import Qt, QObject, pyqtSignal
from PyQt5.QtGui import QColor, QFont
import os
import re
import json
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from datetime import datetime
from plugins.base import PluginBase
from abc import ABC

# 创建一个新的元类来解决冲突
class TimeAnalyzerMeta(type(QObject), type(ABC)):
    pass

# 使用新的元类
class TimeAnalyzerPlugin(QObject, PluginBase, metaclass=TimeAnalyzerMeta):
    # 定义信号
    task_progress = pyqtSignal(str)
    task_completed = pyqtSignal()

    def __init__(self):
        QObject.__init__(self)
        PluginBase.__init__(self)
        # 对话框引用
        self.current_dialog = None

    @property
    def name(self):
        return "仿真时间分析器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "统计所有用例的编译和仿真时间"

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.analyze_time)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

            print(f"成功初始化插件: {self.name}")

        except Exception as e:
            print(f"初始化插件 {self.name} 失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            from PyQt5.QtWidgets import QLabel, QToolButton

            # 创建状态指示器标签
            self.status_label = QLabel("仿真时间分析器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("仿真时间分析器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.current_dialog = None

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def get_time_from_log(self, log_path, is_sim_log=False):
        """从日志文件中提取时间信息

        Args:
            log_path: 日志文件路径
            is_sim_log: 是否是仿真日志，用于区分编译和仿真日志格式
        """
        try:
            if not os.path.exists(log_path):
                return None

            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

                # 尝试匹配xrun格式
                xrun_pattern = r'xrun: Time - (\d+\.?\d*)s'
                if match := re.search(xrun_pattern, content):
                    seconds = float(match.group(1))
                    return round(seconds / 60, 2)

                # 尝试匹配VCS格式
                if "Compilation Performance Summary" in content:
                    if is_sim_log:
                        # 对于仿真日志，先定位到仿真部分
                        if "SimuLation Performance Summary" in content:
                            # 分割内容，只保留仿真部分
                            sim_part = content.split("SimuLation Performance Summary")[-1]
                            # 在仿真部分中查找Elapsed Time
                            sim_pattern = r'Elapsed Time\s+:\s+(\d+)\s+sec'
                            if sim_match := re.search(sim_pattern, sim_part):
                                sim_time = float(sim_match.group(1))
                                return round(sim_time / 60, 2)
                    else:
                        # 编译日志，查找第一个Elapsed time
                        compile_pattern = r'Elapsed time\s+:\s+(\d+)\s+sec'
                        if match := re.search(compile_pattern, content):
                            compile_time = float(match.group(1))
                            return round(compile_time / 60, 2)

        except Exception as e:
            print(f"读取日志 {log_path} 失败: {str(e)}")
        return None

    def analyze_time(self):
        """分析所有用例的编译和仿真时间"""
        try:
            # 获取当前工作目录
            current_dir = os.getcwd()
            time_data = []

            # 遍历目录
            for case_dir in os.listdir(current_dir):
                case_path = os.path.join(current_dir, case_dir)
                if not os.path.isdir(case_path):
                    continue

                # 更新日志文件路径
                log_dir = os.path.join(case_path, "log")

                if not os.path.exists(log_dir):
                    continue

                # 查找编译日志和仿真日志
                compile_log = os.path.join(log_dir, "irun_compile.log")
                sim_log = os.path.join(log_dir, "irun_sim.log")

                # 修改调用方式
                compile_time = self.get_time_from_log(compile_log, False)  # 编译日志
                sim_time = self.get_time_from_log(sim_log, True)      # 仿真日志

                # 如果两个日志都不存在，跳过该用例
                if compile_time is None and sim_time is None:
                    continue

                # 计算总时间
                total_time = 0
                if compile_time:
                    total_time += compile_time
                if sim_time:
                    total_time += sim_time

                # 添加到数据列表
                time_data.append({
                    'case': case_dir,
                    'compile': compile_time or 0,
                    'sim': sim_time or 0,
                    'total': total_time
                })

            if not time_data:
                QMessageBox.warning(self.main_window, "警告", "未找到任何有效的日志文件")
                return

            # 按总时间排序
            time_data.sort(key=lambda x: x['total'], reverse=True)

            # 显示结果
            self.show_results(time_data)

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"分析时间失败：{str(e)}")

    def export_to_excel(self, table):
        """将表格数据导出为Excel文件"""
        try:
            # 获取保存路径
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_name = f"simulation_time_{current_time}.xlsx"
            file_path, _ = QFileDialog.getSaveFileName(
                self.main_window,
                "导出Excel",
                default_name,
                "Excel文件 (*.xlsx)"
            )

            if not file_path:
                return

            # 创建工作簿和工作表
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "仿真时间统计"

            # 写入表头
            headers = ["用例名称", "编译时间(分钟)", "仿真时间(分钟)", "总时间(分钟)"]
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col)
                cell.value = header
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # 写入数据
            for row in range(table.rowCount()):
                for col in range(table.columnCount()):
                    item = table.item(row, col)
                    if item:
                        cell = ws.cell(row=row + 2, column=col + 1)
                        cell.value = item.text()
                        # 对齐方式
                        if col == 0:  # 用例名称列左对齐
                            cell.alignment = Alignment(horizontal="left", vertical="center")
                        else:  # 时间列右对齐
                            cell.alignment = Alignment(horizontal="right", vertical="center")
                        # 总计行加粗
                        if row == table.rowCount() - 1:
                            cell.font = Font(bold=True)
                            cell.fill = PatternFill(start_color="f8f9fa", end_color="f8f9fa", fill_type="solid")

            # 调整列宽
            for col in ws.columns:
                max_length = 0
                column = col[0].column_letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column].width = adjusted_width

            # 保存文件
            wb.save(file_path)
            QMessageBox.information(self.main_window, "成功", "数据已成功导出到Excel文件")

        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"导出Excel失败：{str(e)}")

    def show_results(self, time_data):
        """显示时间统计结果"""
        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("仿真时间统计")
        dialog.resize(800, 600)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        # 连接信号
        self.task_progress.connect(lambda msg: self.main_window.show_message(msg, 3000))
        self.task_completed.connect(lambda: self.main_window.show_message("时间分析完成", 3000))

        layout = QVBoxLayout()
        layout.setSpacing(2)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建表格
        table = QTableWidget()
        table.setColumnCount(4)
        table.setRowCount(len(time_data))

        # 设置表头
        headers = ["用例名称", "编译时间(分钟)", "仿真时间(分钟)", "总时间(分钟)"]
        table.setHorizontalHeaderLabels(headers)

        # 优化表格样式
        table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                gridline-color: #d0d0d0;
                border: 1px solid #cccccc;
                border-radius: 3px;
                selection-background-color: #e8f0fe;
                selection-color: #000000;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #f0f0f0;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 5px;
                border: none;
                border-bottom: 2px solid #dee2e6;
                border-right: 1px solid #dee2e6;
                font-weight: bold;
            }
            QHeaderView::section:pressed {
                background-color: #e9ecef;
            }
            QHeaderView::section:hover {
                background-color: #e9ecef;
            }
        """)

        # 设置表头和列宽调整属性
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 用例名称列可手动调整
        for i in range(1, 4):
            header.setSectionResizeMode(i, QHeaderView.Interactive)  # 时间列可手动调整

        # 设置初始列宽
        table.setColumnWidth(0, 300)  # 用例名称列宽
        table.setColumnWidth(1, 120)  # 编译时间列宽
        table.setColumnWidth(2, 120)  # 仿真时间列宽
        table.setColumnWidth(3, 120)  # 总时间列宽

        # 设置表格属性
        table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        table.setSelectionMode(QTableWidget.SingleSelection)  # 单行选择
        table.setAlternatingRowColors(True)  # 交替行颜色
        table.setSortingEnabled(True)  # 允许排序

        # 填充数据
        total_compile = 0
        total_sim = 0
        total_time = 0

        for row, data in enumerate(time_data):
            # 累计总时间
            total_compile += data['compile']
            total_sim += data['sim']
            total_time += data['total']

            # 设置用例名称
            name_item = QTableWidgetItem(data['case'])
            name_item.setToolTip(data['case'])  # 添加提示
            table.setItem(row, 0, name_item)

            # 设置时间数据（右对齐）
            items = [
                QTableWidgetItem(f"{data['compile']:.2f}"),
                QTableWidgetItem(f"{data['sim']:.2f}"),
                QTableWidgetItem(f"{data['total']:.2f}")
            ]

            for col, item in enumerate(items, 1):
                item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
                # 设置为数字类型以便正确排序
                item.setData(Qt.DisplayRole, float(item.text()))
                table.setItem(row, col, item)

        # 添加总计行
        table.setRowCount(len(time_data) + 1)
        total_row = len(time_data)

        total_label = QTableWidgetItem("总计")
        total_label.setFont(QFont("", -1, QFont.Bold))
        total_label.setBackground(QColor("#f8f9fa"))
        table.setItem(total_row, 0, total_label)

        totals = [total_compile, total_sim, total_time]
        for col, total in enumerate(totals, 1):
            total_item = QTableWidgetItem(f"{total:.2f}")
            total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            total_item.setFont(QFont("", -1, QFont.Bold))
            total_item.setBackground(QColor("#f8f9fa"))
            table.setItem(total_row, col, total_item)

        # 添加表格拉伸属性，以填充可用空间
        table.horizontalHeader().setStretchLastSection(True)  # 最后一列自动拉伸

        # 保存列宽到设置
        def save_column_widths():
            widths = [table.columnWidth(i) for i in range(table.columnCount())]
            settings = {
                "column_widths": widths
            }
            try:
                with open("time_analyzer_settings.json", "w") as f:
                    json.dump(settings, f)
            except Exception as e:
                print(f"保存列宽设置失败: {str(e)}")

        # 从设置加载列宽
        try:
            with open("time_analyzer_settings.json", "r") as f:
                settings = json.load(f)
                widths = settings.get("column_widths", [])
                if len(widths) == table.columnCount():
                    for i, width in enumerate(widths):
                        table.setColumnWidth(i, width)
        except Exception:
            pass  # 如果设置文件不存在或无效，使用默认列宽

        # 在对话框关闭时保存列宽
        dialog.finished.connect(save_column_widths)

        layout.addWidget(table)

        # 在布局底部添加按钮区域
        button_layout = QHBoxLayout()

        # 添加导出按钮
        export_button = QPushButton("导出到Excel")
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        export_button.clicked.connect(lambda: self.export_to_excel(table))
        button_layout.addWidget(export_button)

        # 添加最小化按钮
        minimize_button = QPushButton("最小化窗口")
        minimize_button.clicked.connect(dialog.showMinimized)
        button_layout.addWidget(minimize_button)

        # 添加后台运行按钮
        background_button = QPushButton("后台运行")
        background_button.clicked.connect(lambda: self.run_in_background(dialog))
        button_layout.addWidget(background_button)

        # 添加关闭按钮
        close_button = QPushButton("关闭")
        close_button.setStyleSheet("""
            QPushButton {
                padding: 5px 15px;
                border-radius: 3px;
            }
        """)
        close_button.clicked.connect(dialog.close)
        button_layout.addWidget(close_button)

        # 将按钮布局添加到主布局
        layout.addLayout(button_layout)

        dialog.setLayout(layout)

        # 连接对话框关闭事件
        dialog.finished.connect(self.on_dialog_closed)

        # 显示状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(True)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(True)

        # 非模态显示对话框
        dialog.show()