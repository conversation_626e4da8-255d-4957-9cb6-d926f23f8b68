{% extends 'dashboard/base.html' %}

{% block title %}{{ project.name }} - 项目详情 - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'dashboard:project_list' %}">项目管理</a></li>
                <li class="breadcrumb-item active">{{ project.name }}</li>
            </ol>
        </nav>
        <h2><i class="fas fa-project-diagram me-2"></i>{{ project.name }}</h2>
    </div>
    <div>
        <a href="/admin/dashboard/project/{{ project.id }}/change/" class="btn btn-outline-primary">
            <i class="fas fa-edit me-1"></i>编辑项目
        </a>
    </div>
</div>

<div class="row">
    <!-- 项目基本信息 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>项目信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">项目名称:</dt>
                            <dd class="col-sm-8">{{ project.name }}</dd>
                            
                            <dt class="col-sm-4">负责人:</dt>
                            <dd class="col-sm-8">{{ project.owner.username }}</dd>
                            
                            <dt class="col-sm-4">创建时间:</dt>
                            <dd class="col-sm-8">{{ project.created_at|date:"Y-m-d H:i:s" }}</dd>
                            
                            <dt class="col-sm-4">最后更新:</dt>
                            <dd class="col-sm-8">{{ project.updated_at|date:"Y-m-d H:i:s" }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">SOC阶段:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-primary">{{ project.get_current_soc_phase_display }}</span>
                            </dd>
                            
                            <dt class="col-sm-5">验证阶段:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-info">{{ project.get_current_verification_phase_display }}</span>
                            </dd>
                            
                            <dt class="col-sm-5">状态:</dt>
                            <dd class="col-sm-7">
                                {% if project.is_active %}
                                    <span class="badge bg-success">活跃</span>
                                {% else %}
                                    <span class="badge bg-secondary">非活跃</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
                
                {% if project.description %}
                    <div class="mt-3">
                        <h6>项目描述:</h6>
                        <p class="text-muted">{{ project.description }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>统计信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-primary">{{ case_stats.total }}</h4>
                            <small class="text-muted">总用例数</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-success">{{ case_stats.subsys_pass }}</h4>
                        <small class="text-muted">子系统通过</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-info">{{ case_stats.top_pass }}</h4>
                            <small class="text-muted">TOP通过</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning">{{ bug_stats.open }}</h4>
                        <small class="text-muted">待处理BUG</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 测试计划列表 -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list me-2"></i>测试计划
                </h5>
                <a href="/admin/dashboard/testplan/add/?project={{ project.id }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus me-1"></i>上传测试计划
                </a>
            </div>
            <div class="card-body">
                {% if testplans %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>测试计划名称</th>
                                    <th>子系统</th>
                                    <th>上传时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for testplan in testplans %}
                                    <tr>
                                        <td>
                                            <h6 class="mb-1">{{ testplan.name }}</h6>
                                        </td>
                                        <td>{{ testplan.subsystem_name }}</td>
                                        <td>{{ testplan.uploaded_at|date:"Y-m-d H:i" }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'dashboard:testcase_list' %}?project={{ project.id }}" 
                                                   class="btn btn-outline-primary" title="查看用例">
                                                    <i class="fas fa-list"></i>
                                                </a>
                                                <a href="/admin/dashboard/testplan/{{ testplan.id }}/change/" 
                                                   class="btn btn-outline-secondary" title="编辑">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-list fa-2x text-muted mb-2"></i>
                        <p class="text-muted">暂无测试计划</p>
                        <a href="/admin/dashboard/testplan/add/?project={{ project.id }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>上传第一个测试计划
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- BUG统计 -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bug me-2"></i>BUG统计
                </h5>
                <a href="{% url 'dashboard:bug_list' %}?project={{ project.id }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-danger">{{ bug_stats.total }}</h4>
                            <small class="text-muted">总BUG数</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-warning">{{ bug_stats.open }}</h4>
                        <small class="text-muted">待处理</small>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border-end">
                            <h4 class="text-success">{{ bug_stats.resolved }}</h4>
                            <small class="text-muted">已解决</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <h4 class="text-secondary">{{ bug_stats.closed }}</h4>
                        <small class="text-muted">已关闭</small>
                    </div>
                </div>
                
                <!-- BUG类型分布图 -->
                <div class="mt-3">
                    <canvas id="bugTypeChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进度图表 -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>用例执行进度
                </h5>
            </div>
            <div class="card-body">
                <canvas id="progressChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>用例状态分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="300"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// BUG类型分布图
const bugTypeCtx = document.getElementById('bugTypeChart').getContext('2d');
const bugTypeChart = new Chart(bugTypeCtx, {
    type: 'doughnut',
    data: {
        labels: ['功能性', '性能', '接口', '时序', '功耗', '其他'],
        datasets: [{
            data: [5, 2, 3, 1, 1, 2],
            backgroundColor: [
                '#dc3545',
                '#fd7e14',
                '#ffc107',
                '#20c997',
                '#6f42c1',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 用例执行进度图
const progressCtx = document.getElementById('progressChart').getContext('2d');
const progressChart = new Chart(progressCtx, {
    type: 'line',
    data: {
        labels: ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周'],
        datasets: [{
            label: '子系统级',
            data: [10, 25, 40, 55, 70, 85],
            borderColor: '#0d6efd',
            backgroundColor: 'rgba(13, 110, 253, 0.1)',
            tension: 0.4
        }, {
            label: 'TOP级',
            data: [5, 15, 30, 45, 60, 75],
            borderColor: '#198754',
            backgroundColor: 'rgba(25, 135, 84, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 100,
                ticks: {
                    callback: function(value) {
                        return value + '%';
                    }
                }
            }
        }
    }
});

// 用例状态分布图
const statusCtx = document.getElementById('statusChart').getContext('2d');
const statusChart = new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: ['通过', '失败', '进行中', '未开始'],
        datasets: [{
            data: [{{ case_stats.subsys_pass }}, 5, 3, {{ case_stats.total|add:"-8" }}],
            backgroundColor: [
                '#198754',
                '#dc3545',
                '#ffc107',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
</script>
{% endblock %}
