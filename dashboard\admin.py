from django.contrib import admin
from .models import Project, TestPlan, TestCase, Bug, ExecutionRecord, CaseStatusSummary


@admin.register(Project)
class ProjectAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'current_soc_phase', 'current_verification_phase', 'is_active', 'created_at']
    list_filter = ['current_soc_phase', 'current_verification_phase', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    readonly_fields = ['created_at', 'updated_at']


@admin.register(TestPlan)
class TestPlanAdmin(admin.ModelAdmin):
    list_display = ['name', 'project', 'subsystem_name', 'uploaded_at']
    list_filter = ['project', 'uploaded_at']
    search_fields = ['name', 'subsystem_name']
    readonly_fields = ['uploaded_at']


@admin.register(TestCase)
class TestCaseAdmin(admin.ModelAdmin):
    list_display = ['testcase_name', 'testplan', 'test_category', 'subsys_status', 'top_status', 'owner']
    list_filter = ['testplan', 'test_category', 'subsys_status', 'top_status', 'post_subsys_status', 'post_top_status']
    search_fields = ['testcase_name', 'test_category', 'function_points']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('testplan', 'test_category', 'items', 'test_areas', 'function_points')
        }),
        ('测试详情', {
            'fields': ('test_scope', 'check_point', 'cover', 'testcase_name', 'owner')
        }),
        ('时间记录', {
            'fields': ('start_time', 'end_time', 'actual_time')
        }),
        ('子系统级状态', {
            'fields': ('subsys_phase', 'subsys_status')
        }),
        ('TOP级状态', {
            'fields': ('top_phase', 'top_status')
        }),
        ('后仿状态', {
            'fields': ('post_subsys_phase', 'post_subsys_status', 'post_top_phase', 'post_top_status')
        }),
        ('其他', {
            'fields': ('note', 'created_at', 'updated_at')
        }),
    )


@admin.register(Bug)
class BugAdmin(admin.ModelAdmin):
    list_display = ['bug_id', 'title', 'project', 'bug_type', 'severity', 'status', 'submitter', 'created_at']
    list_filter = ['project', 'bug_type', 'severity', 'status', 'verification_phase', 'created_at']
    search_fields = ['bug_id', 'title', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('基本信息', {
            'fields': ('project', 'bug_id', 'title', 'description')
        }),
        ('分类信息', {
            'fields': ('bug_type', 'severity', 'verification_phase')
        }),
        ('发现信息', {
            'fields': ('discovered_platform', 'discovered_testcase')
        }),
        ('状态和人员', {
            'fields': ('status', 'submitter', 'assignee')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at', 'resolved_at')
        }),
    )


@admin.register(ExecutionRecord)
class ExecutionRecordAdmin(admin.ModelAdmin):
    list_display = ['testcase', 'result', 'start_time', 'end_time', 'duration', 'executor']
    list_filter = ['result', 'start_time', 'executor']
    search_fields = ['testcase__testcase_name', 'command']
    readonly_fields = ['created_at', 'duration']

    def save_model(self, request, obj, form, change):
        # 自动计算执行时长
        if obj.start_time and obj.end_time:
            obj.duration = obj.end_time - obj.start_time
        super().save_model(request, obj, form, change)


@admin.register(CaseStatusSummary)
class CaseStatusSummaryAdmin(admin.ModelAdmin):
    list_display = ['testplan', 'total_cases', 'subsys_pass', 'top_pass', 'updated_at']
    list_filter = ['testplan', 'updated_at']
    readonly_fields = ['updated_at']
