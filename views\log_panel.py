"""
日志面板视图组件
"""
import sys
import os
import time
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTextEdit, QApplication
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QProcess, QProcessEnvironment

# 尝试注册QProcess::ExitStatus类型，如果失败则忽略
try:
    from PyQt5.QtCore import qRegisterMetaType
    qRegisterMetaType('QProcess::ExitStatus')
except ImportError:
    # 如果qRegisterMetaType不可用，我们将使用直接连接
    pass
from PyQt5.QtGui import QIcon
from utils.resource_monitor import ResourceMonitor
from views.virtual_log_view import VirtualLogView
from utils.adaptive_log_handler import AdaptiveLogHandler
from utils.adaptive_refresh_manager import get_adaptive_refresh_manager
from utils.memory_optimizer import get_memory_optimizer, get_string_pool
from utils.fast_execution_path import get_fast_execution_path

# 确保QApplication在全局范围内可用
from PyQt5.QtWidgets import QApplication
from utils.performance_monitor import PerformanceMonitor

class LogPanel(QWidget):
    """日志面板，用于显示执行日志"""

    # 定义信号
    execution_started = pyqtSignal(str)  # case_name
    execution_finished = pyqtSignal(str, int)  # case_name, exit_code
    execution_stopped = pyqtSignal(str)  # case_name

    # 常量定义 - 优化性能参数
    MAX_LOG_LENGTH = 200000  # 设置最大日志长度
    VISIBLE_REFRESH_INTERVAL = 300  # 可见tab刷新间隔(ms)，减少刷新频率提升性能
    HIDDEN_REFRESH_INTERVAL = 2000   # 隐藏tab刷新间隔(ms)，进一步减少不可见tab的刷新频率
    MIN_BATCH_SIZE = 16384  # 最小批处理日志大小(16KB)，增加批处理大小减少刷新频率
    FORCE_FLUSH_SIZE = 65536  # 强制刷新大小(64KB)，防止缓冲区过大
    MIN_FLUSH_INTERVAL = 0.2  # 最小刷新间隔(秒)，防止过于频繁的刷新
    MAX_PROCESS_EVENTS_INTERVAL = 2.0  # 强制更新UI的最大间隔(秒)

    def __init__(self, case_name, command, parent=None):
        """
        初始化日志面板

        Args:
            case_name (str): 用例名称
            command (str): 执行命令
            parent (QWidget, optional): 父组件
        """
        super().__init__(parent)
        self.case_name = case_name
        self.command = command
        self.process = None
        self.resource_monitor = ResourceMonitor()
        self.resource_monitor.resources_updated.connect(self.on_resources_updated)

        # 使用简单的缓冲区机制，参考备份文件的实现
        self.log_buffer = []  # 主日志缓冲区
        self.log_batch = []   # 批量处理缓冲区
        self.last_flush_time = time.time()
        self.last_output_time = time.time()  # 上次输出时间
        self.pending_flush = False  # 是否有待刷新的内容
        self._flush_lock = False  # 刷新锁，防止并发刷新
        self._pending_flush_timer = None  # 待刷新定时器
        self._force_flush_needed = False  # 强制刷新标志

        # 使用全局性能监控器（优化）
        self.performance_monitor = PerformanceMonitor.instance()
        try:
            self.performance_monitor.performance_alert.connect(self.on_performance_alert)
        except RuntimeError:
            # 如果连接失败，创建新的监控器
            self.performance_monitor = PerformanceMonitor()
            self.performance_monitor.performance_alert.connect(self.on_performance_alert)

        # 使用全局自适应刷新管理器
        self.refresh_manager = get_adaptive_refresh_manager()

        # 第二阶段优化：内存优化器和字符串池
        self.memory_optimizer = get_memory_optimizer()
        self.string_pool = get_string_pool()
        self.fast_execution_path = get_fast_execution_path()

        # 注册对象到内存管理器
        self.memory_optimizer.register_object(self, self._cleanup_callback)

        # 创建独立的定时器，确保每个LogPanel都有自己的刷新机制
        self.log_timer = QTimer()
        self.log_timer.timeout.connect(self._timer_flush)
        self.last_process_events_time = time.time()
        self.log_completed = False
        self.is_visible = True
        self.auto_scroll = True  # 自动滚动标志
        self._timer_active = False  # 定时器激活状态
        self.init_ui()

        # 设置初始刷新间隔
        self._start_timer()

        # 启动资源监控 - 降低频率提升性能
        self.resource_monitor.start_monitoring(interval=5.0)

        # 启动性能监控 - 全局监控器可能已经启动
        if not self.performance_monitor._running:
            self.performance_monitor.start_monitoring(interval=5.0)

    def cleanup_monitors(self):
        """清理监控器"""
        try:
            # 清理性能监控器连接（不停止全局监控器）
            if hasattr(self, 'performance_monitor') and self.performance_monitor is not None:
                try:
                    # 只断开信号连接，不停止全局监控器
                    self.performance_monitor.performance_alert.disconnect(self.on_performance_alert)
                except (RuntimeError, AttributeError, TypeError):
                    pass

                # 不设置为None，因为这是全局单例
                # self.performance_monitor = None

            # 第二阶段优化：清理内存优化器相关资源
            self._cleanup_callback()

            # 停止并清理资源监控器
            if hasattr(self, 'resource_monitor') and self.resource_monitor is not None:
                try:
                    # 断开信号连接
                    self.resource_monitor.resources_updated.disconnect()
                except (RuntimeError, AttributeError):
                    pass

                try:
                    # 停止监控
                    if hasattr(self.resource_monitor, 'stop_monitoring'):
                        self.resource_monitor.stop_monitoring()
                except (RuntimeError, AttributeError):
                    pass

                self.resource_monitor = None

        except Exception as e:
            print(f"清理监控器时出错: {str(e)}")

    def cleanup(self):
        """显式清理方法，在LogPanel被移除时调用"""
        try:
            # 停止定时器
            if hasattr(self, 'log_timer') and self.log_timer is not None:
                self.log_timer.stop()

            # 清理监控器
            self.cleanup_monitors()

            # 停止进程
            if hasattr(self, 'process') and self.process is not None:
                self.stop_execution()

        except Exception as e:
            print(f"清理LogPanel时出错: {str(e)}")

    def __del__(self):
        """析构函数，确保进程被正确终止"""
        try:
            # 停止定时器
            if hasattr(self, 'log_timer') and self.log_timer is not None:
                try:
                    self.log_timer.stop()
                except Exception:
                    pass

            # 清理监控器
            self.cleanup_monitors()

            # 停止进程
            if hasattr(self, 'process') and self.process is not None:
                try:
                    self.stop_execution()
                except Exception:
                    pass

            # 清空引用
            self.process = None

        except Exception:
            # 忽略析构函数中的所有错误
            pass

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(3)
        layout.setContentsMargins(5, 5, 5, 5)

        # 状态显示区域布局
        status_layout = QHBoxLayout()
        status_layout.setSpacing(5)

        # 状态标签，显示用例执行状态
        self.status_label = QLabel("状态: 准备执行")
        self.status_label.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #2980b9;
                padding: 3px;
                background-color: #ecf0f1;
                border-radius: 3px;
            }
        """)

        status_layout.addWidget(self.status_label, stretch=3)

        # 重新执行按钮，用于重新执行当前tab指令
        self.re_run_btn = QPushButton("重新执行")
        self.re_run_btn.setIcon(QIcon.fromTheme("view-refresh", QIcon()))
        self.re_run_btn.clicked.connect(self.start_execution)
        self.re_run_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        status_layout.addWidget(self.re_run_btn, stretch=1)

        # 停止按钮，用于停止用例执行
        self.stop_btn = QPushButton("停止")
        self.stop_btn.setIcon(QIcon.fromTheme("media-playback-stop", QIcon()))
        self.stop_btn.clicked.connect(self.stop_execution)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 3px 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
            QPushButton:pressed {
                background-color: #a93226;
            }
        """)
        status_layout.addWidget(self.stop_btn, stretch=1)

        layout.addLayout(status_layout)

        # 添加命令预览文本框
        self.cmd_preview = QTextEdit()
        self.cmd_preview.setReadOnly(True)
        self.cmd_preview.setFixedHeight(60)  # 固定高度
        self.cmd_preview.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 9pt;
                background-color: #2d2d2d;
                color: #e6e6e6;
                border: 1px solid #1a1a1a;
                border-radius: 3px;
                padding: 5px;
            }
        """)
        self.cmd_preview.setText(f"执行命令:\n{self.command}")
        layout.addWidget(self.cmd_preview)

        # 添加日志文本框（使用虚拟滚动视图）
        self.log_text = VirtualLogView()
        self.log_text.setStyleSheet("""
            VirtualLogView {
                font-family: "Consolas", "Courier New", monospace;
                font-size: 9pt;
                background-color: #ffffff;
                color: #333333;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
        """)
        # 设置字体
        from PyQt5.QtGui import QFont
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)

        self.setLayout(layout)

    def start_execution(self):
        """开始执行用例"""
        if self.process is not None:
            self.stop_execution()

        # 清空日志显示和缓冲区
        self.log_text.clear()

        # 强制更新UI
        QApplication.processEvents()

        # 清空缓冲区
        self.log_buffer.clear()
        self.log_batch.clear()

        # 重置定时器
        self._restart_timer()

        # 清空命令预览，重新显示当前命令
        self.cmd_preview.clear()
        self.cmd_preview.setText(f"执行命令:\n{self.command}")

        # 设置自动滚动
        self.log_text.setAutoScroll(True)
        self.auto_scroll = True

        # 检查资源使用情况（非阻塞）
        try:
            if hasattr(self, 'resource_monitor') and self.resource_monitor is not None:
                high_usage, cpu_percent, mem_percent = self.resource_monitor.check_resources()

                if high_usage:
                    self.status_label.setText(f"状态: 资源过高 (CPU:{cpu_percent:.1f}%, 内存:{mem_percent:.1f}%)")
                else:
                    # 更新状态标签
                    self.status_label.setText("状态: 执行中...")
            else:
                self.status_label.setText("状态: 执行中...")
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                print(f"资源监控错误: {str(e)}")
                self.resource_monitor = None
                self.status_label.setText("状态: 执行中...")
            else:
                raise
        except Exception as e:
            print(f"检查资源时出错: {str(e)}")
            self.status_label.setText("状态: 执行中...")

        # 创建进程
        self.process = QProcess(self)

        # 设置进程环境变量，确保子进程可以显示自己的通知窗口
        env = QProcessEnvironment.systemEnvironment()
        # 添加特殊环境变量，允许子进程创建自己的窗口
        env.insert("QT_PROCESS_SEPARATE_UI", "1")
        # 设置环境变量
        self.process.setProcessEnvironment(env)

        # 合并标准输出和标准错误
        self.process.setProcessChannelMode(QProcess.MergedChannels)

        # 强化信号连接，确保信号不会丢失
        # 尝试使用QueuedConnection，如果失败则使用直接连接
        try:
            self.process.readyReadStandardOutput.connect(self.handle_output, Qt.QueuedConnection)
            self.process.readyReadStandardError.connect(self.handle_output, Qt.QueuedConnection)
        except TypeError:
            # 如果QueuedConnection失败，使用直接连接
            self.process.readyReadStandardOutput.connect(self.handle_output)
            self.process.readyReadStandardError.connect(self.handle_output)

        # finished信号始终使用直接连接，避免QProcess::ExitStatus类型问题
        self.process.finished.connect(self.handle_finished)

        # 添加多层输出检查机制 - 优化性能
        # 1. 主要检查定时器 - 每100ms检查一次，减少频率
        self.output_check_timer = QTimer()
        self.output_check_timer.timeout.connect(self._check_pending_output)
        self.output_check_timer.start(100)

        # 2. 备用检查定时器 - 每500ms强制检查，减少频率
        self.backup_check_timer = QTimer()
        self.backup_check_timer.timeout.connect(self._backup_output_check)
        self.backup_check_timer.start(500)

        current_dir = os.getcwd()
        self.process.setWorkingDirectory(current_dir)

        # 修改进程启动方式，确保子进程可以显示自己的通知窗口
        if sys.platform == 'win32':
            # Windows下使用cmd.exe启动，但允许子进程创建自己的窗口
            self.process.start('cmd.exe', ['/c', self.command])
        else:
            # Linux下使用bash -c启动，但允许子进程创建自己的窗口
            # 使用setsid命令创建新会话，确保子进程可以显示自己的通知
            self.process.start('/bin/bash', ['-c', f"setsid {self.command}"])

        # 等待进程启动
        if not self.process.waitForStarted(5000):  # 等待5秒
            self.status_label.setText("状态: 启动失败")
            return

        # 发出执行开始信号
        self.execution_started.emit(self.case_name)

        # 只在必要时强制更新UI
        current_time = time.time()
        if current_time - self.last_process_events_time >= self.MAX_PROCESS_EVENTS_INTERVAL:
            QApplication.processEvents()
            self.last_process_events_time = current_time

    def stop_execution(self):
        """停止进程执行"""
        if self.process is not None and self.process.state() == QProcess.Running:
            try:
                # 简化进程终止逻辑，回退到与旧代码类似的实现
                if sys.platform == 'win32':
                    # Windows下直接使用kill()
                    self.process.kill()
                else:
                    # Linux下使用terminate()更温和地结束进程
                    self.process.terminate()
                    # 给进程一点时间来终止
                    time.sleep(0.1)
                    # 如果进程仍在运行，强制终止
                    if self.process.state() == QProcess.Running:
                        self.process.kill()

                self.log_text.append("\n[执行已终止]\n")
                self.status_label.setText("状态: 已终止")

                # 发出执行停止信号
                self.execution_stopped.emit(self.case_name)
            except Exception as e:
                self.log_text.append(f"\n[终止进程时出错: {str(e)}]\n")
                print(f"终止进程时出错: {str(e)}")

    def handle_output(self):
        """处理进程输出"""
        if self.process is None:
            return

        try:
            # 检查进程状态
            if self.process.state() != QProcess.Running:
                return

            # 读取所有可用输出，使用多种方式确保不遗漏
            raw_output = self.process.readAllStandardOutput()
            if not raw_output:
                # 尝试读取错误输出
                raw_output = self.process.readAllStandardError()

            if not raw_output:
                return

            # 优化的编码处理，减少尝试次数
            raw_bytes = bytes(raw_output)

            # 首先尝试UTF-8，大多数情况下都能成功
            try:
                output = raw_bytes.decode('utf-8', errors='strict')
            except UnicodeDecodeError:
                # UTF-8失败时，根据平台选择备用编码
                if sys.platform == 'win32':
                    try:
                        output = raw_bytes.decode('gbk', errors='strict')
                    except UnicodeDecodeError:
                        # 最后使用replace模式的UTF-8
                        output = raw_bytes.decode('utf-8', errors='replace')
                else:
                    # Linux/Mac直接使用replace模式的UTF-8
                    output = raw_bytes.decode('utf-8', errors='replace')

            # 只有在有内容时才处理
            if not output:
                return

            current_time = time.time()
            self.last_output_time = current_time

            # 添加到批处理缓冲区
            self.log_batch.append(output)

            # 计算当前批处理缓冲区大小
            batch_size = sum(len(text) for text in self.log_batch)

            # 改进的刷新策略，更激进的刷新
            should_flush_immediately = False
            should_schedule_flush = False

            # 1. 立即刷新：缓冲区过大或包含重要内容
            if batch_size >= self.FORCE_FLUSH_SIZE:
                should_flush_immediately = True
                self._force_flush_needed = True
            elif self.is_visible and ('\n' in output or batch_size >= self.MIN_BATCH_SIZE):
                # 可见标签页优先处理
                time_since_last_flush = current_time - self.last_flush_time
                if time_since_last_flush >= self.MIN_FLUSH_INTERVAL:
                    should_flush_immediately = True
                else:
                    should_schedule_flush = True

            # 2. 延迟刷新：达到最小批处理大小
            elif batch_size >= self.MIN_BATCH_SIZE:
                should_schedule_flush = True

            # 3. 对于不可见标签页，也要确保定期刷新
            elif not self.is_visible and batch_size > 0:
                should_schedule_flush = True

            # 4. 强制刷新：如果长时间没有刷新
            elif batch_size > 0 and (current_time - self.last_flush_time) > 1.0:
                should_flush_immediately = True
                self._force_flush_needed = True

            # 执行立即刷新
            if should_flush_immediately:
                self._move_batch_to_buffer()
                self._schedule_immediate_flush()
            elif should_schedule_flush:
                self.pending_flush = True
                self._schedule_delayed_flush()

        except Exception as e:
            # 静默处理错误，避免控制台输出影响性能
            pass

    def _move_batch_to_buffer(self):
        """将批处理内容移动到主缓冲区"""
        if self.log_batch:
            self.log_buffer.append(''.join(self.log_batch))
            self.log_batch.clear()

    def _schedule_immediate_flush(self):
        """安排立即刷新"""
        if not self._flush_lock:
            # 直接调用刷新，不使用QTimer.singleShot
            self.flush_log_buffer()

    def _schedule_delayed_flush(self):
        """安排延迟刷新"""
        # 取消之前的延迟刷新
        if self._pending_flush_timer is not None:
            self._pending_flush_timer.stop()
            self._pending_flush_timer = None

        # 创建新的延迟刷新定时器
        from PyQt5.QtCore import QTimer
        self._pending_flush_timer = QTimer()
        self._pending_flush_timer.setSingleShot(True)
        self._pending_flush_timer.timeout.connect(self._handle_delayed_flush)

        # 根据可见性设置延迟时间 - 优化性能
        delay = 100 if self.is_visible else 500
        self._pending_flush_timer.start(delay)

    def _handle_delayed_flush(self):
        """处理延迟刷新"""
        self._pending_flush_timer = None
        if self.pending_flush and self.log_batch:
            self._move_batch_to_buffer()
            self.pending_flush = False
            self.flush_log_buffer()

    def _start_timer(self):
        """启动定时器（使用自适应间隔）"""
        if not self._timer_active:
            # 计算当前缓冲区大小作为日志活动指标
            log_activity = sum(len(text) for text in self.log_batch) + sum(len(text) for text in self.log_buffer)

            # 使用自适应刷新管理器获取间隔
            interval = self.refresh_manager.get_refresh_interval(
                self.is_visible,
                log_activity,
                self.case_name
            )

            self.log_timer.start(interval)
            self._timer_active = True

    def _stop_timer(self):
        """停止定时器"""
        if self._timer_active:
            self.log_timer.stop()
            self._timer_active = False

    def _restart_timer(self):
        """重启定时器"""
        self._stop_timer()
        self._start_timer()

    def _timer_flush(self):
        """定时器触发的刷新（自适应版本）"""
        try:
            # 检查是否有待处理的内容
            if self.pending_flush and self.log_batch:
                self._move_batch_to_buffer()
                self.pending_flush = False

            # 执行刷新
            if self.log_buffer or self.log_batch:
                self.flush_log_buffer()

            # 动态调整定时器间隔
            if self._timer_active and not self.log_completed:
                # 计算当前日志活动量
                log_activity = sum(len(text) for text in self.log_batch) + sum(len(text) for text in self.log_buffer)

                # 获取新的刷新间隔
                new_interval = self.refresh_manager.get_refresh_interval(
                    self.is_visible,
                    log_activity,
                    self.case_name
                )

                # 如果间隔变化超过20%，则更新定时器
                current_interval = self.log_timer.interval()
                if abs(new_interval - current_interval) > current_interval * 0.2:
                    self.log_timer.setInterval(new_interval)

            # 如果进程已完成且没有更多内容，可以停止定时器
            if (self.log_completed and
                not self.log_buffer and
                not self.log_batch and
                not self.pending_flush):
                self._stop_timer()

        except Exception as e:
            # 静默处理定时器错误
            pass

    def _check_pending_output(self):
        """检查是否有待处理的输出，防止遗漏"""
        if self.process is None or self.process.state() != QProcess.Running:
            # 进程不在运行，停止检查
            if hasattr(self, 'output_check_timer') and self.output_check_timer is not None:
                self.output_check_timer.stop()
            if hasattr(self, 'backup_check_timer') and self.backup_check_timer is not None:
                self.backup_check_timer.stop()
            return

        try:
            # 主动检查是否有可读的输出
            bytes_available = self.process.bytesAvailable()
            if bytes_available > 0:
                self.handle_output()

        except Exception as e:
            # 静默处理错误
            pass

    def _backup_output_check(self):
        """备用输出检查，更强制性的检查"""
        if self.process is None or self.process.state() != QProcess.Running:
            if hasattr(self, 'backup_check_timer') and self.backup_check_timer is not None:
                self.backup_check_timer.stop()
            return

        try:
            # 强制读取所有可用输出
            bytes_available = self.process.bytesAvailable()
            if bytes_available > 0:
                self.handle_output()

            # 检查是否有长时间未刷新的情况
            current_time = time.time()
            time_since_last_flush = current_time - self.last_flush_time

            # 如果有待处理内容但长时间未刷新，强制刷新
            if (self.log_batch or self.pending_flush) and time_since_last_flush > 2.0:
                self._force_flush_needed = True
                if self.log_batch:
                    self._move_batch_to_buffer()
                    self.pending_flush = False
                self.flush_log_buffer()

        except Exception as e:
            # 静默处理错误
            pass

    def on_resources_updated(self, high_usage, cpu_percent, mem_percent):
        """
        处理资源更新信号

        Args:
            high_usage (bool): 资源使用是否过高
            cpu_percent (float): CPU使用率
            mem_percent (float): 内存使用率
        """
        try:
            # 检查对象是否仍然有效
            if not hasattr(self, 'resource_monitor') or self.resource_monitor is None:
                return

            # 只在进程运行时更新状态
            if self.process is not None and self.process.state() == QProcess.Running:
                if high_usage:
                    self.status_label.setText(f"状态: 资源过高 (CPU:{cpu_percent:.1f}%, 内存:{mem_percent:.1f}%)")
                else:
                    self.status_label.setText("状态: 执行中...")
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                print(f"资源监控错误: {str(e)}")
                # 清理无效的监控器引用
                self.resource_monitor = None
            else:
                raise
        except Exception as e:
            print(f"处理资源更新时出错: {str(e)}")

    def on_performance_alert(self, message, metrics):
        """
        处理性能警报信号

        Args:
            message (str): 警报消息
            metrics (PerformanceMetrics): 性能指标
        """
        try:
            # 检查对象是否仍然有效
            if not hasattr(self, 'performance_monitor') or self.performance_monitor is None:
                return

            # 只在进程运行时处理警报
            if self.process is not None and self.process.state() == QProcess.Running:
                # 更新状态标签
                self.status_label.setText(f"状态: 性能警报 - {message}")

                # 输出警告
                print(f"性能警报: {message}, 用例: {self.case_name}")
        except RuntimeError as e:
            if "wrapped C/C++ object" in str(e):
                print(f"性能监控错误: {str(e)}")
                # 清理无效的监控器引用
                self.performance_monitor = None
            else:
                raise
        except Exception as e:
            print(f"处理性能警报时出错: {str(e)}")

    def flush_log_buffer(self):
        """刷新日志缓冲区，改进可靠性和并发处理"""
        # 防止并发刷新，但不阻塞重要的刷新
        if self._flush_lock and not self._force_flush_needed:
            return

        try:
            self._flush_lock = True
            current_time = time.time()

            # 检查是否有待处理的内容
            has_pending = self.pending_flush and self.log_batch
            has_buffer = bool(self.log_buffer)

            # 如果没有内容需要刷新，直接返回
            if not has_pending and not has_buffer:
                return

            # 检查刷新间隔限制，但强制刷新时忽略间隔
            time_since_last_flush = current_time - self.last_flush_time
            min_interval = self.MIN_FLUSH_INTERVAL

            # 对于不可见的标签页，使用更长的间隔，但不要太长
            if not self.is_visible and not self.log_completed and not self._force_flush_needed:
                min_interval = max(min_interval, 0.5)  # 最多0.5秒间隔，而不是1秒

            # 如果间隔不够且不是强制刷新，则跳过
            if (time_since_last_flush < min_interval and
                not self.log_completed and
                not self._force_flush_needed):
                return

            # 合并待处理的批处理内容
            if has_pending and self.log_batch:
                self.log_buffer.append(''.join(self.log_batch))
                self.log_batch.clear()
                self.pending_flush = False

            # 处理主缓冲区
            if self.log_buffer:
                log_text = ''.join(self.log_buffer)
                self.log_buffer.clear()

                if hasattr(self, 'log_text'):
                    current_text = self.log_text.text()

                    # 检查是否需要裁剪日志
                    if len(current_text) + len(log_text) > self.MAX_LOG_LENGTH:
                        # 保留后半部分的日志
                        total_text = current_text + log_text
                        keep_length = self.MAX_LOG_LENGTH // 2
                        current_text = total_text[-keep_length:]
                        self.log_text.clear()
                        self.log_text.setText(current_text)
                    else:
                        # 直接追加新日志
                        self.log_text.append(log_text)

                    # 处理滚动条
                    if hasattr(self.log_text, 'verticalScrollBar'):
                        scrollbar = self.log_text.verticalScrollBar()
                        at_bottom = scrollbar.value() >= scrollbar.maximum() - 10

                        # 如果需要自动滚动
                        if self.auto_scroll or at_bottom:
                            scrollbar.setValue(scrollbar.maximum())
                    elif hasattr(self.log_text, 'setAutoScroll'):
                        # 对于VirtualLogView，使用setAutoScroll方法
                        self.log_text.setAutoScroll(self.auto_scroll)

            self.last_flush_time = current_time
            self._force_flush_needed = False  # 重置强制刷新标志

        except Exception as e:
            # 静默处理刷新错误
            pass
        finally:
            self._flush_lock = False

    def flush_final_log(self):
        """确保所有日志都被刷新显示"""
        try:
            # 读取所有剩余输出
            if self.process:
                remaining_output = self.process.readAllStandardOutput()
                if remaining_output:
                    raw_bytes = bytes(remaining_output)
                    output = None

                    # 使用优化的编码处理逻辑
                    try:
                        output = raw_bytes.decode('utf-8', errors='strict')
                    except UnicodeDecodeError:
                        if sys.platform == 'win32':
                            try:
                                output = raw_bytes.decode('gbk', errors='strict')
                            except UnicodeDecodeError:
                                output = raw_bytes.decode('utf-8', errors='replace')
                        else:
                            output = raw_bytes.decode('utf-8', errors='replace')

                    self.log_batch.append(output)

            # 合并所有待处理的日志
            if self.log_batch:
                self.log_buffer.append(''.join(self.log_batch))
                self.log_batch.clear()

            if self.log_buffer:
                log_text = ''.join(self.log_buffer)
                self.log_buffer.clear()

                if hasattr(self, 'log_text'):
                    current_text = self.log_text.text()
                    total_text = current_text + log_text

                    # 如果总长度超过限制，只保留后半部分
                    if len(total_text) > self.MAX_LOG_LENGTH:
                        total_text = total_text[-self.MAX_LOG_LENGTH:]
                        self.log_text.clear()
                        self.log_text.setText(total_text)
                    else:
                        self.log_text.append(log_text)

                    # 根据自动滚动设置决定是否滚动到底部
                    if self.auto_scroll:
                        if hasattr(self.log_text, 'verticalScrollBar'):
                            self.log_text.verticalScrollBar().setValue(
                                self.log_text.verticalScrollBar().maximum()
                            )
                        elif hasattr(self.log_text, 'setAutoScroll'):
                            # 对于VirtualLogView，使用setAutoScroll方法
                            self.log_text.setAutoScroll(True)

            # 强制更新界面
            QApplication.processEvents()

        except Exception as e:
            # 静默处理最终日志刷新错误
            pass

        finally:
            # 确保计时器停止
            self.log_timer.stop()
            self.log_completed = True

    def handle_finished(self):
        """处理进程结束事件"""
        if self.process is None:
            return

        try:
            # 在获取退出码之前，先等待一小段时间，确保所有输出都已经准备好
            QApplication.processEvents()
            time.sleep(0.2)  # 稍长的等待时间，确保所有输出都已经准备好
            QApplication.processEvents()

            exit_code = self.process.exitCode()

            # 确保最后的日志被完全刷新 - 只调用一次，避免重复
            self.flush_final_log()

            # 不再进行额外的刷新，避免重复日志
            # 直接更新状态和添加提示信息

            if exit_code == 0:
                self.status_label.setText("状态: 执行完成")
                # 添加提示信息
                self.log_text.append("\n[执行完成] 可以使用工具栏按钮查看波形或日志\n")
            else:
                self.status_label.setText(f"状态: 执行失败 (退出码: {exit_code})")
                # 添加错误提示
                self.log_text.append(f"\n[执行失败] 退出码: {exit_code}\n")

            # 确保滚动到底部（因为添加了提示信息）
            self.auto_scroll = True
            self.log_text.setAutoScroll(True)
            self.log_text._scroll_to_bottom()

            # 发出执行完成信号
            self.execution_finished.emit(self.case_name, exit_code)

            # 不再显示执行完成通知弹窗，让底层runsim脚本自己处理通知

        except Exception as e:
            self.status_label.setText("状态: 执行异常")
            self.log_text.append(f"\n[错误] 处理进程结束时发生错误: {str(e)}\n")
            # 确保错误信息也能滚动到底部
            self.auto_scroll = True
            self.log_text.setAutoScroll(True)
            self.log_text._scroll_to_bottom()
        finally:
            # 停止所有输出检查定时器
            if hasattr(self, 'output_check_timer') and self.output_check_timer is not None:
                self.output_check_timer.stop()
                self.output_check_timer = None
            if hasattr(self, 'backup_check_timer') and self.backup_check_timer is not None:
                self.backup_check_timer.stop()
                self.backup_check_timer = None

            # 停止延迟刷新定时器
            if hasattr(self, '_pending_flush_timer') and self._pending_flush_timer is not None:
                self._pending_flush_timer.stop()
                self._pending_flush_timer = None

            # 释放进程资源但不关闭标签页
            self.process = None
            self.log_completed = True

            # 确保最终日志被刷新
            self._force_flush_needed = True
            if self.log_batch:
                self._move_batch_to_buffer()
                self.pending_flush = False
            if self.log_buffer:
                self.flush_log_buffer()

            # 强制更新UI
            QApplication.processEvents()
            self.last_process_events_time = time.time()

    def showEvent(self, event):
        """标签页显示时恢复快速更新"""
        super().showEvent(event)
        if not self.is_visible:
            self.set_visible(True)

    def hideEvent(self, event):
        """标签页隐藏时切换到慢速更新"""
        super().hideEvent(event)
        if self.is_visible:
            self.set_visible(False)

    def set_visible(self, visible):
        """设置tab可见性，使用改进的定时器管理"""
        # 避免重复设置
        if self.is_visible == visible:
            return

        old_visible = self.is_visible
        self.is_visible = visible

        try:
            # 重启定时器以应用新的间隔
            if old_visible != visible:
                self._restart_timer()

            # 如果变为可见，立即刷新一次以显示积累的日志
            if visible and not old_visible:
                # 先处理待刷新的内容
                if self.pending_flush and self.log_batch:
                    self._move_batch_to_buffer()
                    self.pending_flush = False

                # 立即刷新
                if self.log_buffer:
                    self._force_flush_needed = True
                    self.flush_log_buffer()

        except Exception as e:
            print(f"设置可见性时出错: {str(e)}")

    def _cleanup_callback(self):
        """内存优化器清理回调"""
        try:
            cleaned_objects = 0

            # 清理日志缓冲区中的重复字符串
            if hasattr(self, 'log_buffer') and self.log_buffer:
                # 使用字符串池优化重复字符串
                optimized_buffer = []
                for text in self.log_buffer:
                    if isinstance(text, str):
                        optimized_text = self.string_pool.intern_string(text)
                        optimized_buffer.append(optimized_text)
                    else:
                        optimized_buffer.append(text)

                if len(optimized_buffer) < len(self.log_buffer):
                    cleaned_objects += len(self.log_buffer) - len(optimized_buffer)
                    self.log_buffer = optimized_buffer

            # 清理批处理缓冲区
            if hasattr(self, 'log_batch') and self.log_batch:
                # 限制批处理缓冲区大小
                if len(self.log_batch) > 1000:
                    self.log_batch = self.log_batch[-500:]  # 保留最新的500项
                    cleaned_objects += 500

            # 清理虚拟日志视图的缓存
            if hasattr(self, 'log_text') and hasattr(self.log_text, 'get_cache_stats'):
                cache_stats = self.log_text.get_cache_stats()
                if cache_stats['cache_size'] > 30:  # 如果缓存过大
                    self.log_text._clear_render_cache()
                    cleaned_objects += cache_stats['cache_size']

            return cleaned_objects

        except Exception as e:
            print(f"清理回调错误: {e}")
            return 0
