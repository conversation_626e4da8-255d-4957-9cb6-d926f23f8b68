"""
Dashboard工具类
"""
import pandas as pd
import os
from django.conf import settings
from django.utils import timezone
from .models import TestCase, TestPlan, CaseStatusSummary


class TestPlanImporter:
    """测试计划Excel文件导入器"""
    
    def __init__(self, testplan):
        self.testplan = testplan
        self.excel_file = testplan.excel_file
        
    def import_testcases(self):
        """导入测试用例"""
        try:
            # 读取Excel文件
            file_path = self.excel_file.path

            # 读取TP sheet
            tp_df = pd.read_excel(file_path, sheet_name='TP', header=3)  # 从第4行开始读取数据

            # 获取项目名称和子系统名称（从前几行读取）
            header_df = pd.read_excel(file_path, sheet_name='TP', header=None, nrows=3)

            # 解析项目名称（格式：Project : 项目名）
            project_name = ""
            if len(header_df) > 0 and not pd.isna(header_df.iloc[0, 1]):
                project_line = str(header_df.iloc[0, 1]).strip()
                if project_line.startswith('Project :') or project_line.startswith('Project:'):
                    project_name = project_line.split(':', 1)[1].strip()
                else:
                    project_name = project_line

            # 解析子系统名称（格式：Subsys : 子系统名）
            subsystem_name = ""
            if len(header_df) > 1 and not pd.isna(header_df.iloc[1, 1]):
                subsys_line = str(header_df.iloc[1, 1]).strip()
                if subsys_line.startswith('Subsys :') or subsys_line.startswith('Subsys:'):
                    subsystem_name = subsys_line.split(':', 1)[1].strip()
                else:
                    subsystem_name = subsys_line

            # 处理项目和子系统
            project_updated = self._handle_project_and_subsystem(project_name, subsystem_name)

            # 更新TestPlan的子系统名称
            if subsystem_name:
                self.testplan.subsystem_name = subsystem_name
                self.testplan.save()
            
            # 清除现有的测试用例
            TestCase.objects.filter(testplan=self.testplan).delete()
            
            # 导入测试用例
            imported_count = 0
            for index, row in tp_df.iterrows():
                if pd.isna(row.iloc[0]) or row.iloc[0] == '':  # 跳过空行
                    continue
                    
                testcase = TestCase(
                    testplan=self.testplan,
                    test_category=str(row.iloc[0]) if not pd.isna(row.iloc[0]) else "",  # A列
                    items=str(row.iloc[1]) if not pd.isna(row.iloc[1]) else "",  # B列
                    test_areas=str(row.iloc[2]) if not pd.isna(row.iloc[2]) else "",  # C列
                    function_points=str(row.iloc[3]) if not pd.isna(row.iloc[3]) else "",  # D列
                    test_scope=str(row.iloc[4]) if not pd.isna(row.iloc[4]) else "",  # E列
                    check_point=str(row.iloc[5]) if not pd.isna(row.iloc[5]) else "",  # F列
                    cover=str(row.iloc[6]) if not pd.isna(row.iloc[6]) else "",  # G列
                    testcase_name=str(row.iloc[7]) if not pd.isna(row.iloc[7]) else "",  # H列
                    owner=str(row.iloc[11]) if not pd.isna(row.iloc[11]) else "",  # L列
                    subsys_phase=str(row.iloc[12]) if not pd.isna(row.iloc[12]) else "",  # M列
                    top_phase=str(row.iloc[14]) if not pd.isna(row.iloc[14]) else "",  # O列
                    post_subsys_phase=str(row.iloc[16]) if not pd.isna(row.iloc[16]) else "",  # Q列
                    post_top_phase=str(row.iloc[18]) if not pd.isna(row.iloc[18]) else "",  # S列
                    note=str(row.iloc[20]) if not pd.isna(row.iloc[20]) else "",  # U列
                )
                
                # 处理时间字段
                if not pd.isna(row.iloc[8]):  # I列 - 开始时间
                    try:
                        testcase.start_time = pd.to_datetime(row.iloc[8])
                    except:
                        pass
                        
                if not pd.isna(row.iloc[9]):  # J列 - 结束时间
                    try:
                        testcase.end_time = pd.to_datetime(row.iloc[9])
                    except:
                        pass
                
                # 处理状态字段
                status_mapping = {
                    '未开始': 'not_started',
                    '进行中': 'on_going',
                    '通过': 'pass',
                    '失败': 'fail',
                    '跳过': 'skip',
                    'Pass': 'pass',
                    'Fail': 'fail',
                    'On-Going': 'on_going',
                    'Not Started': 'not_started',
                    'Skip': 'skip',
                }
                
                # N列 - 子系统状态
                if not pd.isna(row.iloc[13]):
                    status_text = str(row.iloc[13]).strip()
                    testcase.subsys_status = status_mapping.get(status_text, 'not_started')
                
                # P列 - TOP状态
                if not pd.isna(row.iloc[15]):
                    status_text = str(row.iloc[15]).strip()
                    testcase.top_status = status_mapping.get(status_text, 'not_started')
                
                # R列 - 后仿子系统状态
                if not pd.isna(row.iloc[17]):
                    status_text = str(row.iloc[17]).strip()
                    testcase.post_subsys_status = status_mapping.get(status_text, 'not_started')
                
                # T列 - 后仿TOP状态
                if not pd.isna(row.iloc[19]):
                    status_text = str(row.iloc[19]).strip()
                    testcase.post_top_status = status_mapping.get(status_text, 'not_started')
                
                testcase.save()
                imported_count += 1
            
            # 更新用例状态汇总
            self.update_case_status_summary()
            
            return {
                'success': True,
                'message': f'成功导入 {imported_count} 个测试用例',
                'imported_count': imported_count
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'导入失败: {str(e)}',
                'imported_count': 0
            }
    
    def update_case_status_summary(self):
        """更新用例状态汇总"""
        testcases = TestCase.objects.filter(testplan=self.testplan)
        
        # 获取或创建状态汇总记录
        summary, created = CaseStatusSummary.objects.get_or_create(
            testplan=self.testplan,
            defaults={'total_cases': 0}
        )
        
        # 统计各种状态的用例数量
        summary.total_cases = testcases.count()
        
        # 子系统级统计
        summary.subsys_not_started = testcases.filter(subsys_status='not_started').count()
        summary.subsys_on_going = testcases.filter(subsys_status='on_going').count()
        summary.subsys_pass = testcases.filter(subsys_status='pass').count()
        summary.subsys_fail = testcases.filter(subsys_status='fail').count()
        
        # TOP级统计
        summary.top_not_started = testcases.filter(top_status='not_started').count()
        summary.top_on_going = testcases.filter(top_status='on_going').count()
        summary.top_pass = testcases.filter(top_status='pass').count()
        summary.top_fail = testcases.filter(top_status='fail').count()
        
        # 后仿统计
        summary.post_subsys_not_started = testcases.filter(post_subsys_status='not_started').count()
        summary.post_subsys_on_going = testcases.filter(post_subsys_status='on_going').count()
        summary.post_subsys_pass = testcases.filter(post_subsys_status='pass').count()
        summary.post_subsys_fail = testcases.filter(post_subsys_status='fail').count()
        
        summary.post_top_not_started = testcases.filter(post_top_status='not_started').count()
        summary.post_top_on_going = testcases.filter(post_top_status='on_going').count()
        summary.post_top_pass = testcases.filter(post_top_status='pass').count()
        summary.post_top_fail = testcases.filter(post_top_status='fail').count()
        
        summary.save()
        
        return summary

    def _handle_project_and_subsystem(self, project_name, subsystem_name):
        """处理项目和子系统的创建或关联"""
        from .models import Project
        from django.contrib.auth.models import User

        try:
            # 如果Excel中指定了项目名称，尝试匹配或创建项目
            if project_name and project_name != self.testplan.project.name:
                # 查找是否存在同名项目
                existing_project = Project.objects.filter(name=project_name).first()

                if existing_project:
                    # 如果存在，更新TestPlan的项目关联
                    old_project = self.testplan.project
                    self.testplan.project = existing_project
                    self.testplan.save()
                    print(f"✓ 测试计划已关联到现有项目: {project_name}")
                else:
                    # 如果不存在，创建新项目
                    # 使用当前项目的owner作为新项目的owner
                    new_project = Project.objects.create(
                        name=project_name,
                        description=f"从TestPlan自动创建的项目: {project_name}",
                        owner=self.testplan.project.owner,
                        current_soc_phase=self.testplan.project.current_soc_phase,
                        current_verification_phase=self.testplan.project.current_verification_phase
                    )

                    # 更新TestPlan的项目关联
                    old_project = self.testplan.project
                    self.testplan.project = new_project
                    self.testplan.save()
                    print(f"✓ 创建新项目并关联: {project_name}")

            # 记录子系统信息
            if subsystem_name:
                print(f"✓ 子系统信息: {subsystem_name}")

            return True

        except Exception as e:
            print(f"⚠ 处理项目和子系统时出错: {e}")
            return False


class TestPlanExporter:
    """测试计划Excel文件导出器 - 严格按照TestPlan_Template.py格式导出"""

    def __init__(self, testplan):
        self.testplan = testplan

    def export_to_excel(self):
        """导出为Excel文件，严格按照21列标准格式"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils import get_column_letter

            # 获取测试用例数据
            testcases = TestCase.objects.filter(testplan=self.testplan).order_by('items')

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "TP"

            # 定义样式
            header_font = Font(name='Arial', size=10, bold=True, color='000000')
            normal_font = Font(name='Arial', size=9, color='000000')

            # 黄色背景（表头）
            yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
            # 绿色背景（PASS状态）
            green_fill = PatternFill(start_color='00FF00', end_color='00FF00', fill_type='solid')
            # 红色背景（FAIL状态）
            red_fill = PatternFill(start_color='FF0000', end_color='FF0000', fill_type='solid')
            # 灰色背景（N/A状态）
            gray_fill = PatternFill(start_color='C0C0C0', end_color='C0C0C0', fill_type='solid')

            # 边框样式
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 居中对齐
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

            # 第一行：项目信息
            project_name = self.testplan.project.name
            ws['A1'] = f'Project: {project_name}'
            ws.merge_cells('A1:U1')
            ws['A1'].font = header_font
            ws['A1'].fill = yellow_fill
            ws['A1'].alignment = center_alignment
            ws['A1'].border = thin_border

            # 第二行：子系统信息
            subsystem_name = self.testplan.subsystem_name or 'Unknown_Subsystem'
            ws['A2'] = f'Subsystem: {subsystem_name}'
            ws.merge_cells('A2:U2')
            ws['A2'].font = header_font
            ws['A2'].fill = yellow_fill
            ws['A2'].alignment = center_alignment
            ws['A2'].border = thin_border

            # 第三行和第四行：表头（严格按照21列A-U格式）
            headers_row3 = [
                'Test Category', 'Items', 'Test Areas', 'Function points', 'Test Scope', 'Check Point',
                'Cover', 'TestCase Name', 'Start Time', 'End Time', 'Actual Time', 'Owner',
                'Subsys', '', 'TOP', '', 'POST_Subsys', '', 'POST_TOP', '', 'Note'
            ]

            headers_row4 = [
                '', '', '', '', '', '', '', '', '', '', '', '',
                'Phase', 'Status', 'Phase', 'Status', 'Phase', 'Status', 'Phase', 'Status', ''
            ]

            # 写入第三行表头
            for col, header in enumerate(headers_row3, 1):
                cell = ws.cell(row=3, column=col, value=header)
                cell.font = header_font
                cell.fill = yellow_fill
                cell.alignment = center_alignment
                cell.border = thin_border

            # 写入第四行表头
            for col, header in enumerate(headers_row4, 1):
                cell = ws.cell(row=4, column=col, value=header)
                cell.font = header_font
                cell.fill = yellow_fill
                cell.alignment = center_alignment
                cell.border = thin_border

            # 合并表头单元格
            merge_ranges = [
                'A3:A4', 'B3:B4', 'C3:C4', 'D3:D4', 'E3:E4', 'F3:F4', 'G3:G4', 'H3:H4',
                'I3:I4', 'J3:J4', 'K3:K4', 'L3:L4', 'M3:N3', 'O3:P3', 'Q3:R3', 'S3:T3', 'U3:U4'
            ]

            for range_str in merge_ranges:
                ws.merge_cells(range_str)

            # 状态映射（数据库值到显示值）
            status_mapping = {
                'not_started': 'N/A',
                'on_going': 'On-Going',
                'pass': 'PASS',
                'fail': 'FAIL',
                'skip': 'Skip'
            }

            # 写入测试用例数据（从第5行开始）
            for row_idx, testcase in enumerate(testcases, 5):
                # 准备21列数据（A-U列）
                row_data = [
                    testcase.test_category,  # A列
                    testcase.items,  # B列
                    testcase.test_areas,  # C列
                    testcase.function_points,  # D列
                    testcase.test_scope,  # E列
                    testcase.check_point,  # F列
                    testcase.cover,  # G列
                    testcase.testcase_name,  # H列
                    testcase.start_time.strftime('%Y/%m/%d') if testcase.start_time else '',  # I列
                    testcase.end_time.strftime('%Y/%m/%d') if testcase.end_time else '',  # J列
                    str(testcase.actual_time) if testcase.actual_time else '',  # K列
                    testcase.owner,  # L列
                    testcase.subsys_phase,  # M列
                    status_mapping.get(testcase.subsys_status, testcase.subsys_status),  # N列
                    testcase.top_phase,  # O列
                    status_mapping.get(testcase.top_status, testcase.top_status),  # P列
                    testcase.post_subsys_phase,  # Q列
                    status_mapping.get(testcase.post_subsys_status, testcase.post_subsys_status),  # R列
                    testcase.post_top_phase,  # S列
                    status_mapping.get(testcase.post_top_status, testcase.post_top_status),  # T列
                    testcase.note,  # U列
                ]

                # 写入数据
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = normal_font
                    cell.border = thin_border

                    # 设置对齐方式
                    if col_idx in [1, 2, 3, 4]:  # 文本列左对齐
                        cell.alignment = left_alignment
                    else:  # 其他列居中对齐
                        cell.alignment = center_alignment

                    # 设置状态颜色
                    if value == 'PASS':
                        cell.fill = green_fill
                    elif value == 'FAIL':
                        cell.fill = red_fill
                    elif value == 'N/A':
                        cell.fill = gray_fill

            # 设置列宽（严格按照模板）
            column_widths = {
                'A': 15,  # Test Category
                'B': 20,  # Items
                'C': 25,  # Test Areas
                'D': 35,  # Function points
                'E': 15,  # Test Scope
                'F': 15,  # Check Point
                'G': 15,  # Cover
                'H': 20,  # TestCase Name
                'I': 12,  # Start Time
                'J': 12,  # End Time
                'K': 12,  # Actual Time
                'L': 8,   # Owner
                'M': 8,   # Subsys Phase
                'N': 8,   # Subsys Status
                'O': 8,   # TOP Phase
                'P': 8,   # TOP Status
                'Q': 8,   # POST_Subsys Phase
                'R': 8,   # POST_Subsys Status
                'S': 8,   # POST_TOP Phase
                'T': 8,   # POST_TOP Status
                'U': 15,  # Note
            }

            for col_letter, width in column_widths.items():
                ws.column_dimensions[col_letter].width = width

            # 设置行高
            for row in range(1, len(testcases) + 5):
                ws.row_dimensions[row].height = 25

            # 创建case_status工作表
            self._create_case_status_sheet(wb)

            # 生成文件名
            filename = f"{project_name}_{self.testplan.name}_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = os.path.join(settings.MEDIA_ROOT, 'exports', filename)

            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)

            # 保存文件
            wb.save(file_path)

            return {
                'success': True,
                'file_path': file_path,
                'filename': filename
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'导出失败: {str(e)}'
            }

    def _create_case_status_sheet(self, wb):
        """创建case_status工作表"""
        try:
            import openpyxl
            from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
            from openpyxl.utils import get_column_letter

            ws = wb.create_sheet("case_status for soc")

            # 定义样式
            header_font = Font(name='Arial', size=10, bold=True, color='000000')
            normal_font = Font(name='Arial', size=9, color='000000')
            yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')

            # 边框样式
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 居中对齐
            center_alignment = Alignment(horizontal='center', vertical='center')

            # 表头
            headers = [
                'Category', 'Total', 'DVR1_PASS', 'DVR1_FAIL', 'DVR1_N/A',
                'DVR2_PASS', 'DVR2_FAIL', 'DVR2_N/A', 'DVR3_PASS', 'DVR3_FAIL', 'DVR3_N/A',
                'DVS1_PASS', 'DVS1_FAIL', 'DVS1_N/A', 'DVS2_PASS', 'DVS2_FAIL', 'DVS2_N/A'
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col, value=header)
                cell.font = header_font
                cell.fill = yellow_fill
                cell.alignment = center_alignment
                cell.border = thin_border

            # 获取统计数据
            testcases = TestCase.objects.filter(testplan=self.testplan)

            # 按分类统计
            categories = testcases.values_list('test_category', flat=True).distinct()

            status_data = []
            total_stats = {'total': 0, 'subsys_pass': 0, 'subsys_fail': 0, 'subsys_na': 0,
                          'top_pass': 0, 'top_fail': 0, 'top_na': 0,
                          'post_subsys_pass': 0, 'post_subsys_fail': 0, 'post_subsys_na': 0,
                          'post_top_pass': 0, 'post_top_fail': 0, 'post_top_na': 0}

            for category in categories:
                if not category:
                    continue

                cat_cases = testcases.filter(test_category=category)
                cat_total = cat_cases.count()

                # 子系统级统计（映射到DVR1）
                subsys_pass = cat_cases.filter(subsys_status='pass').count()
                subsys_fail = cat_cases.filter(subsys_status='fail').count()
                subsys_na = cat_total - subsys_pass - subsys_fail

                # TOP级统计（映射到DVR2）
                top_pass = cat_cases.filter(top_status='pass').count()
                top_fail = cat_cases.filter(top_status='fail').count()
                top_na = cat_total - top_pass - top_fail

                # 后仿子系统级统计（映射到DVR3）
                post_subsys_pass = cat_cases.filter(post_subsys_status='pass').count()
                post_subsys_fail = cat_cases.filter(post_subsys_status='fail').count()
                post_subsys_na = cat_total - post_subsys_pass - post_subsys_fail

                # 后仿TOP级统计（映射到DVS1）
                post_top_pass = cat_cases.filter(post_top_status='pass').count()
                post_top_fail = cat_cases.filter(post_top_status='fail').count()
                post_top_na = cat_total - post_top_pass - post_top_fail

                # 添加分类统计
                status_data.append([
                    category, cat_total,
                    subsys_pass, subsys_fail, subsys_na,  # DVR1
                    top_pass, top_fail, top_na,  # DVR2
                    post_subsys_pass, post_subsys_fail, post_subsys_na,  # DVR3
                    post_top_pass, post_top_fail, post_top_na,  # DVS1
                    0, 0, cat_total  # DVS2 (默认为N/A)
                ])

                # 累计到总计
                total_stats['total'] += cat_total
                total_stats['subsys_pass'] += subsys_pass
                total_stats['subsys_fail'] += subsys_fail
                total_stats['subsys_na'] += subsys_na
                total_stats['top_pass'] += top_pass
                total_stats['top_fail'] += top_fail
                total_stats['top_na'] += top_na
                total_stats['post_subsys_pass'] += post_subsys_pass
                total_stats['post_subsys_fail'] += post_subsys_fail
                total_stats['post_subsys_na'] += post_subsys_na
                total_stats['post_top_pass'] += post_top_pass
                total_stats['post_top_fail'] += post_top_fail
                total_stats['post_top_na'] += post_top_na

            # 添加总计行
            status_data.append([
                'TOTAL', total_stats['total'],
                total_stats['subsys_pass'], total_stats['subsys_fail'], total_stats['subsys_na'],
                total_stats['top_pass'], total_stats['top_fail'], total_stats['top_na'],
                total_stats['post_subsys_pass'], total_stats['post_subsys_fail'], total_stats['post_subsys_na'],
                total_stats['post_top_pass'], total_stats['post_top_fail'], total_stats['post_top_na'],
                0, 0, total_stats['total']
            ])

            # 写入统计数据
            for row_idx, row_data in enumerate(status_data, 2):
                for col_idx, value in enumerate(row_data, 1):
                    cell = ws.cell(row=row_idx, column=col_idx, value=value)
                    cell.font = normal_font
                    cell.alignment = center_alignment
                    cell.border = thin_border

            # 设置列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[get_column_letter(col)].width = 12

        except Exception as e:
            print(f"创建case_status工作表时出错: {e}")


def get_runsim_integration_status():
    """获取与RunSim GUI的集成状态"""
    # 这里可以检查RunSim GUI是否在运行，以及相关的集成状态
    return {
        'runsim_running': False,  # 实际应该检查RunSim进程
        'last_execution': None,   # 最后一次执行时间
        'active_cases': 0,        # 当前活跃的用例数
    }
