"""
Dashboard工具类
"""
import pandas as pd
import os
from django.conf import settings
from django.utils import timezone
from .models import TestCase, TestPlan, CaseStatusSummary


class TestPlanImporter:
    """测试计划Excel文件导入器"""
    
    def __init__(self, testplan):
        self.testplan = testplan
        self.excel_file = testplan.excel_file
        
    def import_testcases(self):
        """导入测试用例"""
        try:
            # 读取Excel文件
            file_path = self.excel_file.path

            # 读取TP sheet
            tp_df = pd.read_excel(file_path, sheet_name='TP', header=3)  # 从第4行开始读取数据

            # 获取项目名称和子系统名称（从前几行读取）
            header_df = pd.read_excel(file_path, sheet_name='TP', header=None, nrows=3)

            # 解析项目名称（格式：Project : 项目名）
            project_name = ""
            if len(header_df) > 0 and not pd.isna(header_df.iloc[0, 1]):
                project_line = str(header_df.iloc[0, 1]).strip()
                if project_line.startswith('Project :') or project_line.startswith('Project:'):
                    project_name = project_line.split(':', 1)[1].strip()
                else:
                    project_name = project_line

            # 解析子系统名称（格式：Subsys : 子系统名）
            subsystem_name = ""
            if len(header_df) > 1 and not pd.isna(header_df.iloc[1, 1]):
                subsys_line = str(header_df.iloc[1, 1]).strip()
                if subsys_line.startswith('Subsys :') or subsys_line.startswith('Subsys:'):
                    subsystem_name = subsys_line.split(':', 1)[1].strip()
                else:
                    subsystem_name = subsys_line

            # 处理项目和子系统
            project_updated = self._handle_project_and_subsystem(project_name, subsystem_name)

            # 更新TestPlan的子系统名称
            if subsystem_name:
                self.testplan.subsystem_name = subsystem_name
                self.testplan.save()
            
            # 清除现有的测试用例
            TestCase.objects.filter(testplan=self.testplan).delete()
            
            # 导入测试用例
            imported_count = 0
            for index, row in tp_df.iterrows():
                if pd.isna(row.iloc[0]) or row.iloc[0] == '':  # 跳过空行
                    continue
                    
                testcase = TestCase(
                    testplan=self.testplan,
                    test_category=str(row.iloc[0]) if not pd.isna(row.iloc[0]) else "",  # A列
                    items=str(row.iloc[1]) if not pd.isna(row.iloc[1]) else "",  # B列
                    test_areas=str(row.iloc[2]) if not pd.isna(row.iloc[2]) else "",  # C列
                    function_points=str(row.iloc[3]) if not pd.isna(row.iloc[3]) else "",  # D列
                    test_scope=str(row.iloc[4]) if not pd.isna(row.iloc[4]) else "",  # E列
                    check_point=str(row.iloc[5]) if not pd.isna(row.iloc[5]) else "",  # F列
                    cover=str(row.iloc[6]) if not pd.isna(row.iloc[6]) else "",  # G列
                    testcase_name=str(row.iloc[7]) if not pd.isna(row.iloc[7]) else "",  # H列
                    owner=str(row.iloc[11]) if not pd.isna(row.iloc[11]) else "",  # L列
                    subsys_phase=str(row.iloc[12]) if not pd.isna(row.iloc[12]) else "",  # M列
                    top_phase=str(row.iloc[14]) if not pd.isna(row.iloc[14]) else "",  # O列
                    post_subsys_phase=str(row.iloc[16]) if not pd.isna(row.iloc[16]) else "",  # Q列
                    post_top_phase=str(row.iloc[18]) if not pd.isna(row.iloc[18]) else "",  # S列
                    note=str(row.iloc[20]) if not pd.isna(row.iloc[20]) else "",  # U列
                )
                
                # 处理时间字段
                if not pd.isna(row.iloc[8]):  # I列 - 开始时间
                    try:
                        testcase.start_time = pd.to_datetime(row.iloc[8])
                    except:
                        pass
                        
                if not pd.isna(row.iloc[9]):  # J列 - 结束时间
                    try:
                        testcase.end_time = pd.to_datetime(row.iloc[9])
                    except:
                        pass
                
                # 处理状态字段
                status_mapping = {
                    '未开始': 'not_started',
                    '进行中': 'on_going',
                    '通过': 'pass',
                    '失败': 'fail',
                    '跳过': 'skip',
                    'Pass': 'pass',
                    'Fail': 'fail',
                    'On-Going': 'on_going',
                    'Not Started': 'not_started',
                    'Skip': 'skip',
                }
                
                # N列 - 子系统状态
                if not pd.isna(row.iloc[13]):
                    status_text = str(row.iloc[13]).strip()
                    testcase.subsys_status = status_mapping.get(status_text, 'not_started')
                
                # P列 - TOP状态
                if not pd.isna(row.iloc[15]):
                    status_text = str(row.iloc[15]).strip()
                    testcase.top_status = status_mapping.get(status_text, 'not_started')
                
                # R列 - 后仿子系统状态
                if not pd.isna(row.iloc[17]):
                    status_text = str(row.iloc[17]).strip()
                    testcase.post_subsys_status = status_mapping.get(status_text, 'not_started')
                
                # T列 - 后仿TOP状态
                if not pd.isna(row.iloc[19]):
                    status_text = str(row.iloc[19]).strip()
                    testcase.post_top_status = status_mapping.get(status_text, 'not_started')
                
                testcase.save()
                imported_count += 1
            
            # 更新用例状态汇总
            self.update_case_status_summary()
            
            return {
                'success': True,
                'message': f'成功导入 {imported_count} 个测试用例',
                'imported_count': imported_count
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'导入失败: {str(e)}',
                'imported_count': 0
            }
    
    def update_case_status_summary(self):
        """更新用例状态汇总"""
        testcases = TestCase.objects.filter(testplan=self.testplan)
        
        # 获取或创建状态汇总记录
        summary, created = CaseStatusSummary.objects.get_or_create(
            testplan=self.testplan,
            defaults={'total_cases': 0}
        )
        
        # 统计各种状态的用例数量
        summary.total_cases = testcases.count()
        
        # 子系统级统计
        summary.subsys_not_started = testcases.filter(subsys_status='not_started').count()
        summary.subsys_on_going = testcases.filter(subsys_status='on_going').count()
        summary.subsys_pass = testcases.filter(subsys_status='pass').count()
        summary.subsys_fail = testcases.filter(subsys_status='fail').count()
        
        # TOP级统计
        summary.top_not_started = testcases.filter(top_status='not_started').count()
        summary.top_on_going = testcases.filter(top_status='on_going').count()
        summary.top_pass = testcases.filter(top_status='pass').count()
        summary.top_fail = testcases.filter(top_status='fail').count()
        
        # 后仿统计
        summary.post_subsys_not_started = testcases.filter(post_subsys_status='not_started').count()
        summary.post_subsys_on_going = testcases.filter(post_subsys_status='on_going').count()
        summary.post_subsys_pass = testcases.filter(post_subsys_status='pass').count()
        summary.post_subsys_fail = testcases.filter(post_subsys_status='fail').count()
        
        summary.post_top_not_started = testcases.filter(post_top_status='not_started').count()
        summary.post_top_on_going = testcases.filter(post_top_status='on_going').count()
        summary.post_top_pass = testcases.filter(post_top_status='pass').count()
        summary.post_top_fail = testcases.filter(post_top_status='fail').count()
        
        summary.save()
        
        return summary

    def _handle_project_and_subsystem(self, project_name, subsystem_name):
        """处理项目和子系统的创建或关联"""
        from .models import Project
        from django.contrib.auth.models import User

        try:
            # 如果Excel中指定了项目名称，尝试匹配或创建项目
            if project_name and project_name != self.testplan.project.name:
                # 查找是否存在同名项目
                existing_project = Project.objects.filter(name=project_name).first()

                if existing_project:
                    # 如果存在，更新TestPlan的项目关联
                    old_project = self.testplan.project
                    self.testplan.project = existing_project
                    self.testplan.save()
                    print(f"✓ 测试计划已关联到现有项目: {project_name}")
                else:
                    # 如果不存在，创建新项目
                    # 使用当前项目的owner作为新项目的owner
                    new_project = Project.objects.create(
                        name=project_name,
                        description=f"从TestPlan自动创建的项目: {project_name}",
                        owner=self.testplan.project.owner,
                        current_soc_phase=self.testplan.project.current_soc_phase,
                        current_verification_phase=self.testplan.project.current_verification_phase
                    )

                    # 更新TestPlan的项目关联
                    old_project = self.testplan.project
                    self.testplan.project = new_project
                    self.testplan.save()
                    print(f"✓ 创建新项目并关联: {project_name}")

            # 记录子系统信息
            if subsystem_name:
                print(f"✓ 子系统信息: {subsystem_name}")

            return True

        except Exception as e:
            print(f"⚠ 处理项目和子系统时出错: {e}")
            return False


class TestPlanExporter:
    """测试计划Excel文件导出器"""
    
    def __init__(self, testplan):
        self.testplan = testplan
    
    def export_to_excel(self):
        """导出为Excel文件"""
        try:
            # 获取测试用例数据
            testcases = TestCase.objects.filter(testplan=self.testplan).order_by('items')
            
            # 准备数据
            data = []
            for testcase in testcases:
                row = [
                    testcase.test_category,  # A列
                    testcase.items,  # B列
                    testcase.test_areas,  # C列
                    testcase.function_points,  # D列
                    testcase.test_scope,  # E列
                    testcase.check_point,  # F列
                    testcase.cover,  # G列
                    testcase.testcase_name,  # H列
                    testcase.start_time.strftime('%Y-%m-%d %H:%M:%S') if testcase.start_time else '',  # I列
                    testcase.end_time.strftime('%Y-%m-%d %H:%M:%S') if testcase.end_time else '',  # J列
                    str(testcase.actual_time) if testcase.actual_time else '',  # K列
                    testcase.owner,  # L列
                    testcase.subsys_phase,  # M列
                    testcase.get_subsys_status_display(),  # N列
                    testcase.top_phase,  # O列
                    testcase.get_top_status_display(),  # P列
                    testcase.post_subsys_phase,  # Q列
                    testcase.get_post_subsys_status_display(),  # R列
                    testcase.post_top_phase,  # S列
                    testcase.get_post_top_status_display(),  # T列
                    testcase.note,  # U列
                ]
                data.append(row)
            
            # 创建DataFrame
            columns = [
                'Test Category', 'Items', 'Test Areas', 'Function points', 'Test Scope',
                'Check Point', 'Cover', 'TestCase Name', 'Start Time', 'End Time',
                'Actual Time', 'Owner', 'Subsys Phase', 'Subsys Status', 'TOP Phase',
                'TOP Status', 'POST_Subsys Phase', 'POST_Subsys Status', 'POST_TOP Phase',
                'POST_TOP Status', 'Note'
            ]
            
            df = pd.DataFrame(data, columns=columns)
            
            # 生成文件名
            filename = f"{self.testplan.project.name}_{self.testplan.name}_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = os.path.join(settings.MEDIA_ROOT, 'exports', filename)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 创建Excel文件
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 写入项目信息
                header_data = [
                    ['项目名称', self.testplan.project.name],
                    ['子系统名称', self.testplan.subsystem_name],
                    ['', ''],  # 空行
                ]
                header_df = pd.DataFrame(header_data)
                header_df.to_excel(writer, sheet_name='TP', index=False, header=False)
                
                # 写入用例数据
                df.to_excel(writer, sheet_name='TP', index=False, startrow=4)
                
                # 写入状态汇总
                summary = CaseStatusSummary.objects.filter(testplan=self.testplan).first()
                if summary:
                    summary_data = [
                        ['统计项', '子系统级', 'TOP级', '后仿子系统级', '后仿TOP级'],
                        ['总数', summary.total_cases, summary.total_cases, summary.total_cases, summary.total_cases],
                        ['未开始', summary.subsys_not_started, summary.top_not_started, summary.post_subsys_not_started, summary.post_top_not_started],
                        ['进行中', summary.subsys_on_going, summary.top_on_going, summary.post_subsys_on_going, summary.post_top_on_going],
                        ['通过', summary.subsys_pass, summary.top_pass, summary.post_subsys_pass, summary.post_top_pass],
                        ['失败', summary.subsys_fail, summary.top_fail, summary.post_subsys_fail, summary.post_top_fail],
                    ]
                    summary_df = pd.DataFrame(summary_data)
                    summary_df.to_excel(writer, sheet_name='case_status', index=False, header=False)
            
            return {
                'success': True,
                'file_path': file_path,
                'filename': filename
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'导出失败: {str(e)}'
            }


def get_runsim_integration_status():
    """获取与RunSim GUI的集成状态"""
    # 这里可以检查RunSim GUI是否在运行，以及相关的集成状态
    return {
        'runsim_running': False,  # 实际应该检查RunSim进程
        'last_execution': None,   # 最后一次执行时间
        'active_cases': 0,        # 当前活跃的用例数
    }
