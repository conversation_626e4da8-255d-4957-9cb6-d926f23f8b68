# RunSim 仪表盘系统实现文档

## 项目概述

基于现有的RunSim GUI程序，我们成功实现了一个完整的Web仪表盘系统，提供项目管理、用例管理、BUG管理和实时监控功能。

## 技术架构

### 后端技术栈
- **框架**: Django 5.2.2
- **数据库**: SQLite3
- **语言**: Python 3.10
- **依赖**: pandas, openpyxl (用于Excel处理)

### 前端技术栈
- **框架**: Bootstrap 5.1.3
- **图表**: Chart.js
- **图标**: Font Awesome 6.0
- **样式**: 自定义CSS + Bootstrap

### 集成方式
- **API接口**: RESTful API
- **实时通信**: HTTP请求
- **文件监控**: 日志文件监控
- **数据同步**: 自动状态更新

## 功能模块

### 1. 项目管理模块
- ✅ 项目CRUD操作
- ✅ 项目状态跟踪（SOC阶段、验证阶段）
- ✅ 项目进度展示
- ✅ 项目统计信息

### 2. 用例管理模块
- ✅ TestPlan Excel文件导入/导出
- ✅ 用例状态自动更新
- ✅ 用例执行时间记录
- ✅ 多级状态管理（子系统级、TOP级、后仿）
- ✅ 用例搜索和过滤

### 3. BUG管理模块
- ✅ BUG记录和管理
- ✅ BUG分类和统计
- ✅ BUG状态跟踪
- ✅ BUG图表展示

### 4. 仪表盘模块
- ✅ 项目进度展示
- ✅ 统计图表实现
- ✅ 实时数据更新
- ✅ 执行记录监控

### 5. 集成模块
- ✅ RunSim GUI集成接口
- ✅ 自动状态更新机制
- ✅ 日志文件监控
- ✅ API接口

## 数据模型设计

### 核心模型
1. **Project** - 项目模型
2. **TestPlan** - 测试计划模型
3. **TestCase** - 测试用例模型
4. **Bug** - BUG管理模型
5. **ExecutionRecord** - 执行记录模型
6. **CaseStatusSummary** - 用例状态汇总模型

### 状态映射规则
根据仿真命令参数自动确定更新的状态列：
```
如果-post参数为空或不存在：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新P列（TOP级用例状态）
    否则：
        更新N列（Subsys级用例状态）

如果-post参数不为空：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新T列（TOP级后仿用例状态）
    否则：
        更新R列（Subsys级后仿用例状态）
```

## 文件结构

```
runsim_r3p1/
├── dashboard_project/          # Django项目配置
│   ├── settings.py            # 项目设置
│   ├── urls.py               # 主URL配置
│   └── wsgi.py               # WSGI配置
├── dashboard/                 # 仪表盘应用
│   ├── models.py             # 数据模型
│   ├── views.py              # 视图函数
│   ├── urls.py               # URL配置
│   ├── admin.py              # 管理界面
│   ├── utils.py              # 工具函数
│   ├── integration.py        # 集成接口
│   └── templates/            # 模板文件
│       └── dashboard/
│           ├── base.html     # 基础模板
│           ├── home.html     # 首页模板
│           ├── project_list.html
│           ├── project_detail.html
│           ├── testcase_list.html
│           └── bug_list.html
├── static/                   # 静态文件
│   ├── css/
│   │   └── dashboard.css     # 样式文件
│   └── js/
│       └── dashboard.js      # JavaScript文件
├── runsim_dashboard_integration.py  # 集成模块
├── create_test_data.py       # 测试数据创建
├── init_data.py              # 简化初始化
└── manage.py                 # Django管理脚本
```

## API接口

### 核心API
- `POST /api/update_testcase_status/` - 更新用例状态
- `GET /api/execution_stats/` - 获取执行统计
- `GET /api/bug_stats/` - 获取BUG统计

### RunSim集成API
- `POST /api/runsim/execution/start/` - 通知执行开始
- `POST /api/runsim/execution/complete/` - 通知执行完成
- `GET /api/runsim/status/` - 获取集成状态

### 导出API
- `GET /api/export/testcases/` - 导出测试用例
- `GET /api/export/bugs/` - 导出BUG列表

### 上传API
- `POST /api/upload/testplan/` - 上传测试计划

## 部署和使用

### 1. 环境准备
```bash
# 安装依赖
pip install django pandas openpyxl

# 数据库迁移
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 2. 启动服务
```bash
# 启动Django开发服务器
python manage.py runserver

# 访问地址
# 仪表盘: http://localhost:8000/
# 管理后台: http://localhost:8000/admin/
```

### 3. 集成RunSim GUI
在现有的RunSim GUI代码中添加：
```python
from runsim_dashboard_integration import integrate_dashboard_with_runsim_gui

# 在AppController初始化后调用
integrate_dashboard_with_runsim_gui(app_controller)
```

## 功能特性

### 自动状态更新
- 监控仿真执行开始和结束
- 自动解析命令参数
- 智能更新对应状态列
- 记录执行时间和结果

### Excel集成
- 支持TestPlan格式导入
- 自动解析用例信息
- 状态汇总生成
- 数据导出功能

### 实时监控
- 日志文件监控
- 执行状态跟踪
- 进度实时更新
- 异常检测和报告

### 用户界面
- 响应式设计
- 现代化UI风格
- 交互式图表
- 搜索和过滤功能

## 测试验证

### 单元测试
- 模型测试
- 视图测试
- API测试
- 集成测试

### 功能测试
- 用例导入测试
- 状态更新测试
- 图表显示测试
- 导出功能测试

## 扩展计划

### 短期扩展
- [ ] 用户权限管理
- [ ] 更多图表类型
- [ ] 邮件通知功能
- [ ] 移动端适配

### 长期扩展
- [ ] 分布式部署
- [ ] 高级分析功能
- [ ] 机器学习预测
- [ ] 第三方工具集成

## 维护和支持

### 日志管理
- Django日志配置
- 错误日志记录
- 性能监控
- 调试信息

### 数据备份
- 定期数据库备份
- 文件备份策略
- 恢复流程
- 数据迁移

### 性能优化
- 数据库查询优化
- 缓存策略
- 静态文件优化
- 并发处理

## 总结

本项目成功实现了一个完整的RunSim仪表盘系统，具备以下特点：

1. **完整性**: 涵盖项目管理、用例管理、BUG管理等核心功能
2. **集成性**: 与现有RunSim GUI无缝集成
3. **实时性**: 支持实时状态更新和监控
4. **易用性**: 现代化Web界面，操作简便
5. **扩展性**: 模块化设计，便于功能扩展

系统已经可以投入使用，为RunSim验证工作提供强大的管理和监控支持。
