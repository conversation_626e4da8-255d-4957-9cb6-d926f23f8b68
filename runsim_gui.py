#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RunSim GUI - 仿真运行控制台
这是重构后的入口点脚本，完全替代原有的runsim_gui.py
"""
import sys
import os
import traceback
import subprocess
import time
import threading
import requests
from PyQt5.QtWidgets import QApplication, QMessageBox
from runsim_dashboard_integration import integrate_dashboard_with_runsim_gui

def show_exception_box(exception_type, exception_value, exception_traceback):
    """显示异常对话框"""
    traceback_str = ''.join(traceback.format_exception(
        exception_type, exception_value, exception_traceback))

    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle("应用程序错误")
    msg_box.setText("程序发生错误，请查看详细信息。")
    msg_box.setDetailedText(traceback_str)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.exec_()


def check_dashboard_service():
    """检查仪表盘服务是否运行"""
    try:
        response = requests.get('http://localhost:8000', timeout=2)
        return response.status_code == 200
    except:
        return False


def start_dashboard_service():
    """启动仪表盘服务"""
    try:
        print("正在启动仪表盘服务...")

        # 检查manage.py是否存在
        if not os.path.exists('manage.py'):
            print("错误: 未找到manage.py文件")
            return False

        # 启动Django服务器作为后台进程
        if sys.platform.startswith('win'):
            # Windows系统
            process = subprocess.Popen(
                [sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:
            # Unix/Linux系统
            process = subprocess.Popen(
                [sys.executable, 'manage.py', 'runserver', '127.0.0.1:8000'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )

        # 等待服务启动
        for i in range(30):  # 最多等待30秒
            time.sleep(1)
            if check_dashboard_service():
                print("✓ 仪表盘服务启动成功")
                return True
            print(f"等待仪表盘服务启动... ({i+1}/30)")

        print("⚠ 仪表盘服务启动超时")
        return False

    except Exception as e:
        print(f"启动仪表盘服务时出错: {e}")
        return False


def initialize_dashboard():
    """初始化仪表盘"""
    try:
        # 检查是否已经运行
        if check_dashboard_service():
            print("✓ 仪表盘服务已运行")
            return True

        # 启动服务
        return start_dashboard_service()

    except Exception as e:
        print(f"初始化仪表盘时出错: {e}")
        return False

def main():
    """主函数"""
    # 创建 QApplication 实例
    app = QApplication(sys.argv)

    try:
        # 在后台线程中初始化仪表盘
        dashboard_thread = threading.Thread(target=initialize_dashboard)
        dashboard_thread.daemon = True
        dashboard_thread.start()

        # 导入应用程序控制器
        from controllers.app_controller import AppController

        # 创建应用程序控制器
        app_controller = AppController()

        # 集成仪表盘功能
        try:
            from runsim_dashboard_integration import integrate_dashboard_with_runsim_gui
            # 等待仪表盘初始化完成（最多等待5秒）
            dashboard_thread.join(timeout=5)
            integrate_dashboard_with_runsim_gui(app_controller)
        except ImportError:
            print("⚠ 仪表盘集成模块未找到，跳过集成")
        except Exception as e:
            print(f"⚠ 仪表盘集成失败: {e}")

        # 显示主窗口
        app_controller.show()

        # 运行应用程序事件循环
        sys.exit(app.exec_())
    except Exception as e:
        # 显示异常对话框
        show_exception_box(type(e), e, e.__traceback__)
        sys.exit(1)

if __name__ == "__main__":
    # 设置异常钩子
    sys.excepthook = show_exception_box
    main()
