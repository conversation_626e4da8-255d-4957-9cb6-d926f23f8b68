from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone


class Project(models.Model):
    """项目模型"""
    name = models.CharField(max_length=200, verbose_name="项目名称")
    description = models.TextField(blank=True, verbose_name="项目描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="负责人")

    # SOC阶段
    SOC_PHASES = [
        ('kickoff', 'Kickoff'),
        ('pre_rtl_0_1', 'PreRTL0.1'),
        ('rtl_0_1', 'RTL0.1'),
        ('pre_rtl_0_5', 'PreRTL0.5'),
        ('rtl_0_5', 'RTL0.5'),
        ('pre_rtl_0_9', 'PreRTL0.9'),
        ('rtl_0_9', 'RTL0.9'),
        ('post_sim', '后仿'),
    ]

    # 验证阶段
    VERIFICATION_PHASES = [
        ('dvr1', 'DVR1'),
        ('dvr2', 'DVR2'),
        ('dvr3', 'DVR3'),
        ('dvs1', 'DVS1'),
        ('dvs2', 'DVS2'),
    ]

    current_soc_phase = models.CharField(
        max_length=20,
        choices=SOC_PHASES,
        default='kickoff',
        verbose_name="当前SOC阶段"
    )
    current_verification_phase = models.CharField(
        max_length=20,
        choices=VERIFICATION_PHASES,
        default='dvr1',
        verbose_name="当前验证阶段"
    )

    is_active = models.BooleanField(default=True, verbose_name="是否激活")

    class Meta:
        verbose_name = "项目"
        verbose_name_plural = "项目"
        ordering = ['-updated_at']

    def __str__(self):
        return self.name

    def get_subsystems(self):
        """获取项目下的所有子系统"""
        return TestPlan.objects.filter(project=self).values_list('subsystem_name', flat=True).distinct()


class Subsystem(models.Model):
    """子系统模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name="所属项目")
    name = models.CharField(max_length=200, verbose_name="子系统名称")
    description = models.TextField(blank=True, verbose_name="子系统描述")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "子系统"
        verbose_name_plural = "子系统"
        unique_together = ['project', 'name']
        ordering = ['project', 'name']

    def __str__(self):
        return f"{self.project.name} - {self.name}"


class TestPlan(models.Model):
    """测试计划模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name="所属项目")
    name = models.CharField(max_length=200, verbose_name="测试计划名称")
    subsystem_name = models.CharField(max_length=200, verbose_name="子系统名称")
    excel_file = models.FileField(upload_to='testplans/', verbose_name="Excel文件")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")

    class Meta:
        verbose_name = "测试计划"
        verbose_name_plural = "测试计划"
        ordering = ['-uploaded_at']

    def __str__(self):
        return f"{self.project.name} - {self.name}"


class TestCase(models.Model):
    """测试用例模型"""
    testplan = models.ForeignKey(TestPlan, on_delete=models.CASCADE, verbose_name="所属测试计划")

    # TestPlan表格字段
    test_category = models.CharField(max_length=100, verbose_name="测试分类")  # A列
    items = models.CharField(max_length=100, verbose_name="测试项")  # B列
    test_areas = models.CharField(max_length=200, verbose_name="测试区域")  # C列
    function_points = models.CharField(max_length=200, verbose_name="功能点")  # D列
    test_scope = models.TextField(verbose_name="测试范围")  # E列
    check_point = models.TextField(verbose_name="检查点")  # F列
    cover = models.CharField(max_length=200, verbose_name="覆盖")  # G列
    testcase_name = models.CharField(max_length=200, verbose_name="测试用例名称")  # H列

    # 时间字段
    start_time = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")  # I列
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")  # J列
    actual_time = models.DurationField(null=True, blank=True, verbose_name="实际时间")  # K列

    owner = models.CharField(max_length=100, verbose_name="负责人")  # L列

    # 状态字段
    STATUS_CHOICES = [
        ('not_started', '未开始'),
        ('on_going', '进行中'),
        ('pass', '通过'),
        ('fail', '失败'),
        ('skip', '跳过'),
    ]

    # 子系统级状态
    subsys_phase = models.CharField(max_length=50, blank=True, verbose_name="子系统阶段")  # M列
    subsys_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='not_started',
        verbose_name="子系统状态"
    )  # N列

    # TOP级状态
    top_phase = models.CharField(max_length=50, blank=True, verbose_name="TOP阶段")  # O列
    top_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='not_started',
        verbose_name="TOP状态"
    )  # P列

    # 后仿子系统级状态
    post_subsys_phase = models.CharField(max_length=50, blank=True, verbose_name="后仿子系统阶段")  # Q列
    post_subsys_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='not_started',
        verbose_name="后仿子系统状态"
    )  # R列

    # 后仿TOP级状态
    post_top_phase = models.CharField(max_length=50, blank=True, verbose_name="后仿TOP阶段")  # S列
    post_top_status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='not_started',
        verbose_name="后仿TOP状态"
    )  # T列

    note = models.TextField(blank=True, verbose_name="备注")  # U列

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "测试用例"
        verbose_name_plural = "测试用例"
        ordering = ['testplan', 'items']

    def __str__(self):
        return f"{self.testcase_name}"


class Bug(models.Model):
    """BUG管理模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, verbose_name="所属项目")

    # BUG基本信息
    bug_id = models.CharField(max_length=50, unique=True, verbose_name="BUG ID")
    title = models.CharField(max_length=200, verbose_name="BUG标题")
    description = models.TextField(verbose_name="问题描述")

    # BUG分类
    BUG_TYPES = [
        ('functional', '功能性'),
        ('performance', '性能'),
        ('interface', '接口'),
        ('timing', '时序'),
        ('power', '功耗'),
        ('other', '其他'),
    ]

    bug_type = models.CharField(
        max_length=20,
        choices=BUG_TYPES,
        verbose_name="BUG类型"
    )

    # 严重程度
    SEVERITY_LEVELS = [
        ('critical', '严重'),
        ('major', '主要'),
        ('minor', '次要'),
        ('trivial', '轻微'),
    ]

    severity = models.CharField(
        max_length=20,
        choices=SEVERITY_LEVELS,
        verbose_name="严重程度"
    )

    # 验证阶段
    verification_phase = models.CharField(
        max_length=20,
        choices=Project.VERIFICATION_PHASES,
        verbose_name="验证阶段"
    )

    # 发现信息
    discovered_platform = models.CharField(max_length=100, verbose_name="发现平台")
    discovered_testcase = models.CharField(max_length=200, verbose_name="发现用例")

    # 状态
    STATUS_CHOICES = [
        ('open', '打开'),
        ('in_progress', '处理中'),
        ('resolved', '已解决'),
        ('closed', '已关闭'),
        ('rejected', '已拒绝'),
    ]

    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='open',
        verbose_name="状态"
    )

    # 人员
    submitter = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='submitted_bugs',
        verbose_name="提交者"
    )
    assignee = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='assigned_bugs',
        verbose_name="验证人"
    )

    # 时间
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    resolved_at = models.DateTimeField(null=True, blank=True, verbose_name="解决时间")

    class Meta:
        verbose_name = "BUG"
        verbose_name_plural = "BUG"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.bug_id} - {self.title}"


class ExecutionRecord(models.Model):
    """执行记录模型 - 记录仿真执行的详细信息"""
    testcase = models.ForeignKey(TestCase, on_delete=models.CASCADE, verbose_name="测试用例")

    # 执行参数
    command = models.TextField(verbose_name="执行命令")
    case_param = models.CharField(max_length=200, verbose_name="case参数")
    base_param = models.CharField(max_length=100, blank=True, verbose_name="base参数")
    block_param = models.CharField(max_length=100, blank=True, verbose_name="block参数")
    post_param = models.CharField(max_length=100, blank=True, verbose_name="post参数")

    # 执行结果
    RESULT_CHOICES = [
        ('running', '运行中'),
        ('pass', '通过'),
        ('fail', '失败'),
        ('timeout', '超时'),
        ('error', '错误'),
    ]

    result = models.CharField(
        max_length=20,
        choices=RESULT_CHOICES,
        default='running',
        verbose_name="执行结果"
    )

    # 时间记录
    start_time = models.DateTimeField(verbose_name="开始时间")
    end_time = models.DateTimeField(null=True, blank=True, verbose_name="结束时间")
    duration = models.DurationField(null=True, blank=True, verbose_name="执行时长")

    # 日志文件路径
    log_file_path = models.CharField(max_length=500, blank=True, verbose_name="日志文件路径")

    # 执行者
    executor = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="执行者")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "执行记录"
        verbose_name_plural = "执行记录"
        ordering = ['-start_time']

    def __str__(self):
        return f"{self.testcase.testcase_name} - {self.start_time}"


class CaseStatusSummary(models.Model):
    """用例状态汇总模型 - 对应Excel中的case_status sheet"""
    testplan = models.ForeignKey(TestPlan, on_delete=models.CASCADE, verbose_name="所属测试计划")

    # 统计数据
    total_cases = models.IntegerField(default=0, verbose_name="总用例数")

    # 子系统级统计
    subsys_not_started = models.IntegerField(default=0, verbose_name="子系统未开始")
    subsys_on_going = models.IntegerField(default=0, verbose_name="子系统进行中")
    subsys_pass = models.IntegerField(default=0, verbose_name="子系统通过")
    subsys_fail = models.IntegerField(default=0, verbose_name="子系统失败")

    # TOP级统计
    top_not_started = models.IntegerField(default=0, verbose_name="TOP未开始")
    top_on_going = models.IntegerField(default=0, verbose_name="TOP进行中")
    top_pass = models.IntegerField(default=0, verbose_name="TOP通过")
    top_fail = models.IntegerField(default=0, verbose_name="TOP失败")

    # 后仿统计
    post_subsys_not_started = models.IntegerField(default=0, verbose_name="后仿子系统未开始")
    post_subsys_on_going = models.IntegerField(default=0, verbose_name="后仿子系统进行中")
    post_subsys_pass = models.IntegerField(default=0, verbose_name="后仿子系统通过")
    post_subsys_fail = models.IntegerField(default=0, verbose_name="后仿子系统失败")

    post_top_not_started = models.IntegerField(default=0, verbose_name="后仿TOP未开始")
    post_top_on_going = models.IntegerField(default=0, verbose_name="后仿TOP进行中")
    post_top_pass = models.IntegerField(default=0, verbose_name="后仿TOP通过")
    post_top_fail = models.IntegerField(default=0, verbose_name="后仿TOP失败")

    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "用例状态汇总"
        verbose_name_plural = "用例状态汇总"
        unique_together = ['testplan']

    def __str__(self):
        return f"{self.testplan.name} - 状态汇总"
