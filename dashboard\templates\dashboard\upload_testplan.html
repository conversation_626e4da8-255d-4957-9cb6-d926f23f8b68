{% extends 'dashboard/base.html' %}

{% block title %}上传测试计划 - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-upload me-2"></i>上传测试计划
                    </h4>
                </div>
                <div class="card-body">
                    <form id="uploadForm" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="project_id" class="form-label">选择项目 *</label>
                            <select class="form-select" id="project_id" name="project_id" required>
                                <option value="">请选择项目</option>
                                {% for project in projects %}
                                    <option value="{{ project.id }}">{{ project.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="testplan_name" class="form-label">测试计划名称 *</label>
                            <input type="text" class="form-control" id="testplan_name" name="testplan_name" 
                                   placeholder="请输入测试计划名称" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="excel_file" class="form-label">Excel文件 *</label>
                            <input type="file" class="form-control" id="excel_file" name="excel_file" 
                                   accept=".xlsx,.xls" required>
                            <div class="form-text">
                                支持的文件格式：.xlsx, .xls<br>
                                文件应包含TP和case_status两个工作表
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-1"></i>Excel文件格式要求：</h6>
                                <ul class="mb-0">
                                    <li>第1行：项目名称信息（格式：Project : 项目名）</li>
                                    <li>第2行：子系统名称信息（格式：Subsys : 子系统名）</li>
                                    <li>第3-4行：表头信息</li>
                                    <li>第5行开始：用例数据</li>
                                    <li>必须包含TP工作表（用例数据）</li>
                                    <li>可选包含case_status工作表（状态汇总）</li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard:project_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary" id="uploadBtn">
                                <i class="fas fa-upload me-1"></i>上传文件
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 进度模态框 -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传进度</h5>
            </div>
            <div class="modal-body text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">上传中...</span>
                </div>
                <p id="progressText">正在上传文件，请稍候...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#uploadForm').submit(function(e) {
        e.preventDefault();
        
        // 验证表单
        if (!this.checkValidity()) {
            this.reportValidity();
            return;
        }
        
        // 显示进度模态框
        $('#progressModal').modal('show');
        $('#uploadBtn').prop('disabled', true);
        
        // 创建FormData对象
        var formData = new FormData(this);
        
        // 发送AJAX请求
        $.ajax({
            url: '{% url "dashboard:upload_testplan" %}',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                $('#progressModal').modal('hide');
                
                if (response.success) {
                    // 成功提示
                    showAlert('success', response.message);
                    
                    // 3秒后跳转到项目列表
                    setTimeout(function() {
                        window.location.href = '{% url "dashboard:project_list" %}';
                    }, 3000);
                } else {
                    // 错误提示
                    showAlert('danger', response.message);
                    $('#uploadBtn').prop('disabled', false);
                }
            },
            error: function(xhr, status, error) {
                $('#progressModal').modal('hide');
                $('#uploadBtn').prop('disabled', false);
                
                var message = '上传失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                showAlert('danger', message);
            }
        });
    });
    
    // 文件选择变化时的处理
    $('#excel_file').change(function() {
        var file = this.files[0];
        if (file) {
            var fileName = file.name;
            var fileSize = (file.size / 1024 / 1024).toFixed(2); // MB
            
            // 检查文件大小（限制10MB）
            if (file.size > 10 * 1024 * 1024) {
                showAlert('warning', '文件大小不能超过10MB');
                this.value = '';
                return;
            }
            
            // 检查文件扩展名
            var allowedExtensions = ['.xlsx', '.xls'];
            var fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
            
            if (allowedExtensions.indexOf(fileExtension) === -1) {
                showAlert('warning', '请选择Excel文件（.xlsx或.xls格式）');
                this.value = '';
                return;
            }
            
            console.log('选择的文件:', fileName, '大小:', fileSize + 'MB');
        }
    });
    
    function showAlert(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 添加新的alert
        $('.container-fluid').prepend(alertHtml);
        
        // 滚动到顶部
        $('html, body').animate({scrollTop: 0}, 500);
        
        // 5秒后自动隐藏
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
{% endblock %}
