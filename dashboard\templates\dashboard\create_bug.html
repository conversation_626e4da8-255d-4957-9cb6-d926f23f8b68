{% extends 'dashboard/base.html' %}

{% block title %}新建BUG - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-bug me-2"></i>新建BUG
                    </h4>
                </div>
                <div class="card-body">
                    <form id="bugForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">所属项目 *</label>
                                    <select class="form-select" id="project_id" name="project_id" required>
                                        <option value="">请选择项目</option>
                                        {% for project in projects %}
                                            <option value="{{ project.id }}">{{ project.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bug_id" class="form-label">BUG ID *</label>
                                    <input type="text" class="form-control" id="bug_id" name="bug_id" 
                                           placeholder="例如: BUG-001" required>
                                    <div class="form-text">BUG的唯一标识符</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">BUG标题 *</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   placeholder="请输入BUG标题" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">问题描述 *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      placeholder="请详细描述问题现象、复现步骤等" required></textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="bug_type" class="form-label">BUG类型 *</label>
                                    <select class="form-select" id="bug_type" name="bug_type" required>
                                        <option value="">请选择类型</option>
                                        <option value="functional">功能性</option>
                                        <option value="performance">性能</option>
                                        <option value="interface">接口</option>
                                        <option value="timing">时序</option>
                                        <option value="power">功耗</option>
                                        <option value="other">其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="severity" class="form-label">严重程度 *</label>
                                    <select class="form-select" id="severity" name="severity" required>
                                        <option value="">请选择严重程度</option>
                                        <option value="critical">严重</option>
                                        <option value="major">主要</option>
                                        <option value="minor">次要</option>
                                        <option value="trivial">轻微</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="verification_phase" class="form-label">验证阶段 *</label>
                                    <select class="form-select" id="verification_phase" name="verification_phase" required>
                                        <option value="">请选择验证阶段</option>
                                        <option value="dvr1">DVR1</option>
                                        <option value="dvr2">DVR2</option>
                                        <option value="dvr3">DVR3</option>
                                        <option value="dvs1">DVS1</option>
                                        <option value="dvs2">DVS2</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discovered_platform" class="form-label">发现平台</label>
                                    <input type="text" class="form-control" id="discovered_platform" name="discovered_platform" 
                                           placeholder="例如: FPGA验证平台">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discovered_testcase" class="form-label">发现用例</label>
                                    <input type="text" class="form-control" id="discovered_testcase" name="discovered_testcase" 
                                           placeholder="例如: cpu_basic_test">
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard:bug_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="fas fa-save me-1"></i>创建BUG
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#bugForm').submit(function(e) {
        e.preventDefault();
        
        // 验证表单
        if (!this.checkValidity()) {
            this.reportValidity();
            return;
        }
        
        // 禁用提交按钮
        $('#submitBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>创建中...');
        
        // 发送AJAX请求
        $.ajax({
            url: '{% url "dashboard:create_bug" %}',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    // 成功提示
                    showAlert('success', response.message);
                    
                    // 3秒后跳转到BUG列表
                    setTimeout(function() {
                        window.location.href = '{% url "dashboard:bug_list" %}';
                    }, 3000);
                } else {
                    // 错误提示
                    showAlert('danger', response.message);
                    $('#submitBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>创建BUG');
                }
            },
            error: function(xhr, status, error) {
                $('#submitBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>创建BUG');
                
                var message = '创建失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                showAlert('danger', message);
            }
        });
    });
    
    // 自动生成BUG ID
    $('#project_id').change(function() {
        var projectId = $(this).val();
        if (projectId) {
            // 简单的BUG ID生成逻辑
            var timestamp = new Date().getTime().toString().slice(-6);
            var bugId = 'BUG-' + timestamp;
            $('#bug_id').val(bugId);
        }
    });
    
    function showAlert(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 添加新的alert
        $('.container-fluid').prepend(alertHtml);
        
        // 滚动到顶部
        $('html, body').animate({scrollTop: 0}, 500);
        
        // 5秒后自动隐藏
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
{% endblock %}
