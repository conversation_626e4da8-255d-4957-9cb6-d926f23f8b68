## 功能描述
在此GUI基础上增加项目管理功能，包括仪表盘、用例管理、BUG记录等功能。

## 用例管理
SOC是分阶段的，对于涉及具体为项目Kickoff -> PreRTL0.1 -> RTL0.1 -> PreRTL0.5 -> RLT0.5 -> PreRTL0.9 -> RTL0.9 -> 后仿
而对于验证，分为DVR1 -> DVR2 -> DVR3 -> DVS1 -> DVS2
- 导入用例：支持导入Excel格式的文件，导入后自动生成用例列表。
- 导出用例：支持导出Excel格式的文件，导出后可以保存到本地。
- 解析导入用例，展示在界面中
- 支持在线增加或者删减用例
- 用例TestPlan有两个sheet，TP sheet描述了用例信息，格式如下：
  ![TestPlan格式示例](TestPlan.png)
  第一行提供项目名称信息
  第二行提供子系统名称信息
  第三行和第四行是表头信息，分别表示：
  A列用例分类、B列编号、C列测试范围、D列功能点、E列测试流程、F列检查点、G列覆盖点、H列用例名、I列起始时间、J列结束时间、K列实际时间、M列是子系统级用例验证阶段，N列是子系统及用例验证状态、O列是TOP级用例验证阶段、P列是TOP级用例验证状态、Q列是子系统后仿用例验证阶段、R列是后子系统后仿例验证状态、S列TOP后仿用例用例验证阶段、T列是TOP后仿用例验证状态、U列是备注
  其他行就是用例具体描述
  case_status for soc sheet描述了用例状态汇总，格式如下：
  ![case_status格式示例](case_status.png)
- GUI主界面执行用例，可以通过-case执行的用例名定位到表格具体用例，当点击执行编译和仿真按钮后，记录该用例起始时间并更新表格用例状态为On-Going，当用例PASS，则记录用例结束时间并更新表格用例状态为Pass，当用例FAIL则不更新结束时间但要更新用例状态为Fail。
- 用例状态是区分subsys级、TOP级以及后仿用例的，具体规则如下：当不存在-post选项且-base选项为top或者-block选项为top，则更新P列TOP级用例状态，当不存在-post选项且情况更新N列子系统级用例状
    如果-post选项为空：
        如果-base选项为top或者-block选项为top，则更新P列TOP级用例状态，否则更新N列子系统级用例状态
    如果-post选项不为空
        如果-base选项为top或者-block选项为top，则更新T列TOP级后仿用例状态，否认则更新R列子系统级后仿用例状态

### 表格结构
| 列 | 字段名 | 说明 |
|---|--------|------|
| A | Test Category | 测试分类 |
| B | Items | 测试项 |
| C | Test Areas | 测试区域 |
| D | Function points | 功能点 |
| E | Test Scope | 测试范围 |
| F | Check Point | 检查点 |
| G | Cover | 覆盖 |
| H | TestCase Name | 测试用例名称 |
| I | Start Time | 开始时间 |
| J | End Time | 结束时间 |
| K | Actual Time | 实际时间 |
| L | Owner | 负责人 |
| M | Subsys Phase | 子系统阶段 |
| N | Subsys Status | 子系统状态 |
| O | TOP Phase | TOP阶段 |
| P | TOP Status | TOP状态 |
| Q | POST_Subsys Phase | 后仿子系统阶段 |
| R | POST_Subsys Status | 后仿子系统状态 |
| S | POST_TOP Phase | 后仿TOP阶段 |
| T | POST_TOP Status | 后仿TOP状态 |
| U | Note | 备注 |

为RunSim GUI仪表盘系统新增自动状态更新和统计功能，具体要求如下：
**1. 仿真执行时间记录功能：**
- 当用户在RunSim GUI界面点击"执行仿真和编译"按钮时，自动记录当前时间戳并更新仪表盘数据库中对应用例的Start Time字段（I列）
- 同时将该用例的状态更新为"On-Going"

**2. 用例执行结果自动检测和状态更新功能：**
- 监控仿真执行完成事件，当用例执行结束后：
  - 自动读取用例目录下的irun_sim.log文件的最后50行内容
  - 检测是否包含"SPRD_PASSED"字符串：
    - 如果找到"SPRD_PASSED"：判定为PASS，记录结束时间到End Time字段（J列），更新状态为"PASS"
    - 如果未找到"SPRD_PASSED"：判定为FAIL，不更新结束时间，更新状态为"FAIL"

**3. 用例状态更新的列映射规则：**
根据仿真命令中的参数确定更新哪个状态列：
- 解析执行命令中的-case参数获取用例名，与数据库TestCase Name列（H列）匹配定位用例行
- 根据-post、-base、-block参数确定状态列：
  ```
  如果-post参数为空或不存在：
      如果-base参数为"top" 或 -block参数包含"top"：
          更新P列（TOP级用例状态）
      否则：
          更新N列（Subsys级用例状态）
  
  如果-post参数不为空：
      如果-base参数为"top" 或 -block参数包含"top"：
          更新T列（TOP级后仿用例状态）
      否则：
          更新R列（Subsys级后仿用例状态）
  ```

**4. 仪表盘统计图表功能：**
- 在仪表盘页面新增用例通过率统计图表
- 支持按日统计和按周统计两种模式切换
- 显示内容包括：
  - 每日/每周的用例通过数量
  - 每日/每周的用例总执行数量
  - 累计用例通过总数
  - 用例通过率趋势图

**技术实现要点：**
- 需要监听RunSim GUI的仿真执行事件
- 实现日志文件监控和解析机制
- 确保数据库更新的原子性和一致性
- 图表组件需要支持实时数据更新
- 制定实现该更新的todo list进行任务拆解，每完成一项更新一项计划

### 状态列映射规则实现
根据仿真命令参数自动确定更新的状态列：
- **Subsys级用例**：更新subsys_status列（N列）
- **TOP级用例**：更新top_status列（P列）
- **Subsys级后仿用例**：更新post_subsys_status列（R列）
- **TOP级后仿用例**：更新post_top_status列（T列）

判断逻辑：
```
如果-post参数为空或不存在：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新P列（TOP级用例状态）
    否则：
        更新N列（Subsys级用例状态）

如果-post参数不为空：
    如果-base参数为"top" 或 -block参数包含"top"：
        更新T列（TOP级后仿用例状态）
    否则：
        更新R列（Subsys级后仿用例状态）
```

## BUG管理
- 记录和管理项目中出现的BUG，包括序号、BUG ID、BUG类型、提交SYS、验证阶段、问题描述、发现平台、发现用例等
- 支持图标展示，根据BUG类型、验证阶段、问题严重程度、提交者、验证人等进行分类
- 以周为单位统计BUG总数，包括每周新增等内容

## 仪表盘
- 仪表盘展示项目的整体进度，包括项目进度、测试用例进度、测试用例执行情况、测试用例执行时间等
- 项目进度：根据项目的进度，展示项目的整体进度，包括项目总进度、子系统进度、TOP级用例进度、后仿用例进度等
- Testplan case_status部分解析展示

## 要求
- 该功能使用网页实现
- 前端技术：HTML/CSS/JS
- 后端技术：Python/Django
- 数据库：Sqlite3
- 提供详细的实现步骤并输出实现文档

## 实现计划

### 阶段1: Django项目搭建 ✅
- [x] 创建Django项目结构
- [x] 配置SQLite3数据库
- [x] 设计数据库模型
- [x] 创建基础Web界面框架

### 阶段2: 项目管理模块 ✅
- [x] 项目CRUD操作
- [x] 项目配置管理
- [x] 项目状态跟踪

### 阶段3: 用例管理模块 ✅
- [x] TestPlan Excel文件解析
- [x] 用例数据模型实现
- [x] 用例状态自动更新机制
- [x] 用例执行时间记录

### 阶段4: BUG管理模块 ✅
- [x] BUG记录和管理
- [x] BUG分类和统计
- [x] BUG图表展示

### 阶段5: 仪表盘模块 ✅
- [x] 项目进度展示
- [x] 统计图表实现
- [x] 实时数据更新

### 阶段6: 集成和测试 ✅
- [x] 与现有RunSim GUI集成
- [x] 自动状态更新机制
- [x] 测试和优化

## 实现成果

### 已完成功能
1. **完整的Web仪表盘系统**
   - 现代化响应式界面
   - 项目管理、用例管理、BUG管理
   - 实时数据展示和图表

2. **数据库设计**
   - 6个核心数据模型
   - 完整的关系设计
   - 状态管理和历史记录

3. **Excel集成**
   - TestPlan文件导入/导出
   - 自动解析用例信息
   - 状态汇总生成

4. **RunSim GUI集成**
   - API接口完整
   - 自动状态更新
   - 日志文件监控
   - 集成示例代码

5. **管理功能**
   - Django管理后台
   - 用户权限管理
   - 数据导出功能

### 技术特点
- **后端**: Django + SQLite3
- **前端**: Bootstrap + Chart.js
- **集成**: RESTful API + 文件监控
- **部署**: 开发服务器就绪

### 使用方式
```bash
# 启动服务
python manage.py runserver

# 访问地址
http://localhost:8000/        # 仪表盘
http://localhost:8000/admin/  # 管理后台
```

### 集成方式
```python
# 在RunSim GUI中集成
from runsim_dashboard_integration import integrate_dashboard_with_runsim_gui
integrate_dashboard_with_runsim_gui(app_controller)
```
