{% extends 'dashboard/base.html' %}

{% block title %}编辑用例 - {{ testcase.testcase_name }} - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>编辑用例 - {{ testcase.testcase_name }}
                    </h4>
                </div>
                <div class="card-body">
                    <form id="editForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="testcase_name" class="form-label">用例名称</label>
                                    <input type="text" class="form-control" id="testcase_name" name="testcase_name" 
                                           value="{{ testcase.testcase_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="test_category" class="form-label">测试分类</label>
                                    <input type="text" class="form-control" id="test_category" name="test_category" 
                                           value="{{ testcase.test_category }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="function_points" class="form-label">功能点</label>
                                    <input type="text" class="form-control" id="function_points" name="function_points" 
                                           value="{{ testcase.function_points }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="owner" class="form-label">负责人</label>
                                    <input type="text" class="form-control" id="owner" name="owner" 
                                           value="{{ testcase.owner }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="subsys_status" class="form-label">子系统状态</label>
                                    <select class="form-select" id="subsys_status" name="subsys_status">
                                        <option value="not_started" {% if testcase.subsys_status == 'not_started' %}selected{% endif %}>未开始</option>
                                        <option value="on_going" {% if testcase.subsys_status == 'on_going' %}selected{% endif %}>进行中</option>
                                        <option value="pass" {% if testcase.subsys_status == 'pass' %}selected{% endif %}>通过</option>
                                        <option value="fail" {% if testcase.subsys_status == 'fail' %}selected{% endif %}>失败</option>
                                        <option value="skip" {% if testcase.subsys_status == 'skip' %}selected{% endif %}>跳过</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="top_status" class="form-label">TOP状态</label>
                                    <select class="form-select" id="top_status" name="top_status">
                                        <option value="not_started" {% if testcase.top_status == 'not_started' %}selected{% endif %}>未开始</option>
                                        <option value="on_going" {% if testcase.top_status == 'on_going' %}selected{% endif %}>进行中</option>
                                        <option value="pass" {% if testcase.top_status == 'pass' %}selected{% endif %}>通过</option>
                                        <option value="fail" {% if testcase.top_status == 'fail' %}selected{% endif %}>失败</option>
                                        <option value="skip" {% if testcase.top_status == 'skip' %}selected{% endif %}>跳过</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="post_subsys_status" class="form-label">后仿子系统状态</label>
                                    <select class="form-select" id="post_subsys_status" name="post_subsys_status">
                                        <option value="not_started" {% if testcase.post_subsys_status == 'not_started' %}selected{% endif %}>未开始</option>
                                        <option value="on_going" {% if testcase.post_subsys_status == 'on_going' %}selected{% endif %}>进行中</option>
                                        <option value="pass" {% if testcase.post_subsys_status == 'pass' %}selected{% endif %}>通过</option>
                                        <option value="fail" {% if testcase.post_subsys_status == 'fail' %}selected{% endif %}>失败</option>
                                        <option value="skip" {% if testcase.post_subsys_status == 'skip' %}selected{% endif %}>跳过</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="post_top_status" class="form-label">后仿TOP状态</label>
                                    <select class="form-select" id="post_top_status" name="post_top_status">
                                        <option value="not_started" {% if testcase.post_top_status == 'not_started' %}selected{% endif %}>未开始</option>
                                        <option value="on_going" {% if testcase.post_top_status == 'on_going' %}selected{% endif %}>进行中</option>
                                        <option value="pass" {% if testcase.post_top_status == 'pass' %}selected{% endif %}>通过</option>
                                        <option value="fail" {% if testcase.post_top_status == 'fail' %}selected{% endif %}>失败</option>
                                        <option value="skip" {% if testcase.post_top_status == 'skip' %}selected{% endif %}>跳过</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="note" class="form-label">备注</label>
                            <textarea class="form-control" id="note" name="note" rows="3">{{ testcase.note }}</textarea>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard:testcase_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary" id="saveBtn">
                                <i class="fas fa-save me-1"></i>保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#editForm').submit(function(e) {
        e.preventDefault();
        
        // 禁用保存按钮
        $('#saveBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>保存中...');
        
        // 发送AJAX请求
        $.ajax({
            url: '{% url "dashboard:edit_testcase" testcase.id %}',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    // 成功提示
                    showAlert('success', response.message);
                    
                    // 3秒后跳转到用例列表
                    setTimeout(function() {
                        window.location.href = '{% url "dashboard:testcase_list" %}';
                    }, 3000);
                } else {
                    // 错误提示
                    showAlert('danger', response.message);
                    $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存更改');
                }
            },
            error: function(xhr, status, error) {
                $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存更改');
                
                var message = '保存失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                showAlert('danger', message);
            }
        });
    });
    
    function showAlert(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 添加新的alert
        $('.container-fluid').prepend(alertHtml);
        
        // 滚动到顶部
        $('html, body').animate({scrollTop: 0}, 500);
        
        // 5秒后自动隐藏
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
{% endblock %}
