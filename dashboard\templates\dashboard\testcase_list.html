{% extends 'dashboard/base.html' %}

{% block title %}用例管理 - RunSim 仪表盘系统{% endblock %}

{% csrf_token %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tasks me-2"></i>用例管理</h2>
    <div>
        <a href="{% url 'dashboard:upload_testplan' %}" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i>上传测试计划
        </a>
        <div class="btn-group">
            <button class="btn btn-success dropdown-toggle" type="button" data-bs-toggle="dropdown">
                <i class="fas fa-file-excel me-1"></i>导出Excel
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="#" onclick="exportTestcases('all')">导出所有用例</a></li>
                <li><a class="dropdown-item" href="#" onclick="exportTestcases('current')">导出当前筛选</a></li>
                <li><hr class="dropdown-divider"></li>
                <li><a class="dropdown-item" href="#" onclick="showTestplanExportModal()">按测试计划导出</a></li>
            </ul>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="搜索用例名称...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select project-filter">
                <option value="">所有项目</option>
                {% for project in projects %}
                    <option value="{{ project.id }}" {% if current_project == project.id|stringformat:"s" %}selected{% endif %}>
                        {{ project.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select status-filter">
                <option value="">所有状态</option>
                <option value="not_started" {% if current_status == 'not_started' %}selected{% endif %}>未开始</option>
                <option value="on_going" {% if current_status == 'on_going' %}selected{% endif %}>进行中</option>
                <option value="pass" {% if current_status == 'pass' %}selected{% endif %}>通过</option>
                <option value="fail" {% if current_status == 'fail' %}selected{% endif %}>失败</option>
                <option value="skip" {% if current_status == 'skip' %}selected{% endif %}>跳过</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select category-filter">
                <option value="">所有分类</option>
                <option value="功能测试">功能测试</option>
                <option value="性能测试">性能测试</option>
                <option value="接口测试">接口测试</option>
                <option value="压力测试">压力测试</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>清除过滤
            </button>
        </div>
    </div>
</div>

<!-- 用例趋势图 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>用例执行趋势
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary active" id="weeklyViewBtn" onclick="switchTrendView('weekly')">
                        <i class="fas fa-calendar-week me-1"></i>按周
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="dailyViewBtn" onclick="switchTrendView('daily')">
                        <i class="fas fa-calendar-day me-1"></i>按日
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="toggleChartType()">
                        <i class="fas fa-chart-bar me-1"></i><span id="chartTypeText">柱状图</span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">总用例数</h6>
                            <h4 class="text-primary" id="totalCases">-</h4>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">已通过</h6>
                            <h4 class="text-success" id="totalPassed">-</h4>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="text-center">
                            <h6 class="text-muted">通过率</h6>
                            <h4 class="text-info" id="passRate">-</h4>
                        </div>
                    </div>
                </div>
                <div style="height: 400px;">
                    <canvas id="trendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作 -->
<div class="batch-actions" style="display: none;">
    <div class="alert alert-info">
        <div class="d-flex justify-content-between align-items-center">
            <span>已选择 <span class="batch-count">0</span> 个用例</span>
            <div>
                <button class="btn btn-sm btn-success" onclick="batchUpdateStatus('pass')">
                    <i class="fas fa-check me-1"></i>批量标记通过
                </button>
                <button class="btn btn-sm btn-danger" onclick="batchUpdateStatus('fail')">
                    <i class="fas fa-times me-1"></i>批量标记失败
                </button>
                <button class="btn btn-sm btn-secondary" onclick="clearSelection()">
                    <i class="fas fa-times me-1"></i>取消选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用例列表 -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover searchable filterable">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>用例名称</th>
                            <th>测试分类</th>
                            <th>功能点</th>
                            <th>负责人</th>
                            <th>子系统状态</th>
                            <th>TOP状态</th>
                            <th>后仿状态</th>
                            <th>执行时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for testcase in page_obj %}
                            <tr data-status="{{ testcase.subsys_status }}" 
                                data-project="{{ testcase.testplan.project.id }}"
                                data-category="{{ testcase.test_category }}">
                                <td>
                                    <input type="checkbox" class="form-check-input item-checkbox" 
                                           value="{{ testcase.id }}">
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1">{{ testcase.testcase_name|truncatechars:40 }}</h6>
                                        <small class="text-muted">{{ testcase.testplan.project.name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ testcase.test_category }}</span>
                                </td>
                                <td>{{ testcase.function_points|truncatechars:30 }}</td>
                                <td>{{ testcase.owner }}</td>
                                <td>
                                    <span class="badge status-{{ testcase.subsys_status }}">
                                        {{ testcase.get_subsys_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge status-{{ testcase.top_status }}">
                                        {{ testcase.get_top_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="badge status-{{ testcase.post_subsys_status }} mb-1">
                                            子系统: {{ testcase.get_post_subsys_status_display }}
                                        </span>
                                        <span class="badge status-{{ testcase.post_top_status }}">
                                            TOP: {{ testcase.get_post_top_status_display }}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    {% if testcase.start_time %}
                                        <small class="text-muted">
                                            开始: {{ testcase.start_time|date:"m-d H:i" }}<br>
                                            {% if testcase.end_time %}
                                                结束: {{ testcase.end_time|date:"m-d H:i" }}<br>
                                                耗时: {{ testcase.actual_time }}
                                            {% endif %}
                                        </small>
                                    {% else %}
                                        <span class="text-muted">未执行</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" 
                                                onclick="viewTestcaseDetail({{ testcase.id }})" 
                                                title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{% url 'dashboard:edit_testcase' testcase.id %}"
                                           class="btn btn-outline-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-success" 
                                                onclick="updateStatus({{ testcase.id }}, 'pass')" 
                                                title="标记通过">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                onclick="updateStatus({{ testcase.id }}, 'fail')" 
                                                title="标记失败">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="用例分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无测试用例</h5>
                <p class="text-muted">请先上传测试计划文件</p>
                <a href="{% url 'dashboard:upload_testplan' %}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>上传测试计划
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- 用例详情模态框 -->
<div class="modal fade" id="testcaseDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用例详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="testcaseDetailContent">
                <!-- 用例详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>

<!-- 测试计划导出模态框 -->
<div class="modal fade" id="testplanExportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">按测试计划导出</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="testplanSelect" class="form-label">选择测试计划</label>
                    <select class="form-select" id="testplanSelect">
                        <option value="">请选择测试计划...</option>
                        <!-- 测试计划选项将通过AJAX加载 -->
                    </select>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    导出的Excel文件将严格按照TestPlan标准格式（21列A-U），包含完整的项目信息、表头格式和状态汇总。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="exportSelectedTestplan()">
                    <i class="fas fa-download me-1"></i>导出
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 过滤功能
function clearFilters() {
    $('.project-filter').val('');
    $('.status-filter').val('');
    $('.category-filter').val('');
    $('#searchInput').val('');
    $('.filterable tbody tr').show();
}

// 查看用例详情
function viewTestcaseDetail(testcaseId) {
    $('#testcaseDetailContent').html('<div class="text-center"><span class="loading"></span> 加载中...</div>');
    $('#testcaseDetailModal').modal('show');

    // 通过AJAX加载用例详情
    $.ajax({
        url: '/testcases/' + testcaseId + '/',
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            $('#testcaseDetailContent').html(response);
        },
        error: function(xhr, status, error) {
            $('#testcaseDetailContent').html(
                '<div class="alert alert-danger">加载失败，请稍后重试</div>'
            );
        }
    });
}

// 更新用例状态
function updateStatus(testcaseId, status) {
    if (confirm('确定要更新用例状态吗？')) {
        $.ajax({
            url: '/api/update_testcase_status/',
            type: 'POST',
            data: {
                'testcase_id': testcaseId,
                'status': status,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    showMessage('success', '状态更新成功');
                    // 刷新页面或更新表格行
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showMessage('danger', response.message || '状态更新失败');
                }
            },
            error: function(xhr, status, error) {
                showMessage('danger', '网络错误，请稍后重试');
            }
        });
    }
}

function showMessage(type, message) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>';

    // 移除现有的alert
    $('.alert').remove();

    // 添加新的alert到页面顶部
    $('.container-fluid').prepend(alertHtml);

    // 滚动到顶部
    $('html, body').animate({scrollTop: 0}, 500);

    // 5秒后自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// 批量更新状态
function batchUpdateStatus(status) {
    var selectedIds = [];
    $('.item-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });
    
    if (selectedIds.length === 0) {
        alert('请先选择要更新的用例');
        return;
    }
    
    if (confirm('确定要批量更新 ' + selectedIds.length + ' 个用例的状态吗？')) {
        // 这里应该通过AJAX批量更新状态
        console.log('批量更新用例状态', selectedIds, status);
        // 实际实现时需要调用后端API
    }
}

// 清除选择
function clearSelection() {
    $('.item-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false);
    $('.batch-actions').hide();
}

// 导出用例功能
function exportTestcases(type) {
    var url = '/api/export/testcases/?format=excel';

    if (type === 'current') {
        // 添加当前筛选条件
        var projectFilter = $('.project-filter').val();
        if (projectFilter) {
            url += '&project=' + projectFilter;
        }
    }

    // 直接下载
    window.location.href = url;
}

// 显示测试计划导出模态框
function showTestplanExportModal() {
    $('#testplanExportModal').modal('show');

    // 加载测试计划列表
    $.ajax({
        url: '/api/testplans/',
        type: 'GET',
        success: function(response) {
            var select = $('#testplanSelect');
            select.empty();
            select.append('<option value="">请选择测试计划...</option>');

            if (response.testplans) {
                response.testplans.forEach(function(tp) {
                    select.append('<option value="' + tp.id + '">' + tp.project_name + ' - ' + tp.name + '</option>');
                });
            }
        },
        error: function() {
            showMessage('danger', '加载测试计划列表失败');
        }
    });
}

// 导出选定的测试计划
function exportSelectedTestplan() {
    var testplanId = $('#testplanSelect').val();
    if (!testplanId) {
        alert('请选择要导出的测试计划');
        return;
    }

    // 下载文件
    window.location.href = '/api/export/testcases/?format=excel&testplan=' + testplanId;

    // 关闭模态框
    $('#testplanExportModal').modal('hide');
}

// 趋势图相关变量
let trendChart = null;
let currentTrendView = 'weekly';
let currentChartType = 'line';
let trendData = null;

// 初始化趋势图
function initTrendChart() {
    loadTrendData();
}

// 加载趋势数据
function loadTrendData() {
    const projectFilter = $('.project-filter').val();
    let url = '/api/testcase_trend_stats/';

    if (projectFilter) {
        url += '?project=' + projectFilter;
    }

    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                trendData = response.data;
                updateSummaryStats();
                renderTrendChart();
            } else {
                console.error('加载趋势数据失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载趋势数据失败:', error);
        }
    });
}

// 更新汇总统计
function updateSummaryStats() {
    if (trendData && trendData.summary) {
        $('#totalCases').text(trendData.summary.total_cases);
        $('#totalPassed').text(trendData.summary.total_passed);
        $('#passRate').text(trendData.summary.pass_rate + '%');
    }
}

// 渲染趋势图
function renderTrendChart() {
    if (!trendData) return;

    const ctx = document.getElementById('trendChart').getContext('2d');

    // 销毁现有图表
    if (trendChart) {
        trendChart.destroy();
    }

    const data = currentTrendView === 'weekly' ? trendData.weekly_stats : trendData.daily_stats;
    const labels = data.map(item => currentTrendView === 'weekly' ? item.week_label : item.date_label);
    const newPassData = data.map(item => item.new_pass);
    const cumulativeData = data.map(item => item.cumulative_pass);

    const config = {
        type: currentChartType,
        data: {
            labels: labels,
            datasets: [
                {
                    label: currentTrendView === 'weekly' ? '每周新增通过' : '每日新增通过',
                    data: newPassData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                },
                {
                    label: '累计通过',
                    data: cumulativeData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: currentTrendView === 'weekly' ? '周' : '日期'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '新增通过数'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '累计通过数'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: currentTrendView === 'weekly' ? '用例执行趋势（按周）' : '用例执行趋势（按日）'
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            if (context.datasetIndex === 0) {
                                return '新增通过: ' + context.parsed.y + ' 个';
                            } else {
                                return '累计通过: ' + context.parsed.y + ' 个';
                            }
                        }
                    }
                }
            }
        }
    };

    trendChart = new Chart(ctx, config);
}

// 切换趋势视图（按周/按日）
function switchTrendView(view) {
    currentTrendView = view;

    // 更新按钮状态
    $('#weeklyViewBtn, #dailyViewBtn').removeClass('active');
    if (view === 'weekly') {
        $('#weeklyViewBtn').addClass('active');
    } else {
        $('#dailyViewBtn').addClass('active');
    }

    // 重新渲染图表
    renderTrendChart();
}

// 切换图表类型（折线图/柱状图）
function toggleChartType() {
    currentChartType = currentChartType === 'line' ? 'bar' : 'line';

    // 更新按钮文本
    $('#chartTypeText').text(currentChartType === 'line' ? '柱状图' : '折线图');

    // 重新渲染图表
    renderTrendChart();
}

$(document).ready(function() {
    // 初始化趋势图
    initTrendChart();

    // 多重过滤功能
    function applyFilters() {
        var projectFilter = $('.project-filter').val();
        var statusFilter = $('.status-filter').val();
        var categoryFilter = $('.category-filter').val();
        var searchText = $('#searchInput').val().toLowerCase();

        $('.filterable tbody tr').each(function() {
            var row = $(this);
            var show = true;

            // 项目过滤
            if (projectFilter && row.data('project') != projectFilter) {
                show = false;
            }

            // 状态过滤
            if (statusFilter && row.data('status') != statusFilter) {
                show = false;
            }

            // 分类过滤
            if (categoryFilter && row.data('category') != categoryFilter) {
                show = false;
            }

            // 搜索过滤
            if (searchText && row.text().toLowerCase().indexOf(searchText) === -1) {
                show = false;
            }

            row.toggle(show);
        });
    }

    // 绑定过滤事件
    $('.project-filter, .status-filter, .category-filter').change(function() {
        applyFilters();
        // 项目过滤变化时重新加载趋势数据
        if ($(this).hasClass('project-filter')) {
            loadTrendData();
        }
    });
    $('#searchInput').on('keyup', applyFilters);
});
</script>
{% endblock %}
