{% extends 'dashboard/base.html' %}

{% block title %}用例管理 - RunSim 仪表盘系统{% endblock %}

{% csrf_token %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-tasks me-2"></i>用例管理</h2>
    <div>
        <a href="{% url 'dashboard:upload_testplan' %}" class="btn btn-primary">
            <i class="fas fa-upload me-1"></i>上传测试计划
        </a>
        <button class="btn btn-success export-btn" data-format="excel" data-url="/api/export/testcases/">
            <i class="fas fa-file-excel me-1"></i>导出Excel
        </button>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-4">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="搜索用例名称...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select project-filter">
                <option value="">所有项目</option>
                {% for project in projects %}
                    <option value="{{ project.id }}" {% if current_project == project.id|stringformat:"s" %}selected{% endif %}>
                        {{ project.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select status-filter">
                <option value="">所有状态</option>
                <option value="not_started" {% if current_status == 'not_started' %}selected{% endif %}>未开始</option>
                <option value="on_going" {% if current_status == 'on_going' %}selected{% endif %}>进行中</option>
                <option value="pass" {% if current_status == 'pass' %}selected{% endif %}>通过</option>
                <option value="fail" {% if current_status == 'fail' %}selected{% endif %}>失败</option>
                <option value="skip" {% if current_status == 'skip' %}selected{% endif %}>跳过</option>
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select category-filter">
                <option value="">所有分类</option>
                <option value="功能测试">功能测试</option>
                <option value="性能测试">性能测试</option>
                <option value="接口测试">接口测试</option>
                <option value="压力测试">压力测试</option>
            </select>
        </div>
        <div class="col-md-2">
            <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times me-1"></i>清除过滤
            </button>
        </div>
    </div>
</div>

<!-- 批量操作 -->
<div class="batch-actions" style="display: none;">
    <div class="alert alert-info">
        <div class="d-flex justify-content-between align-items-center">
            <span>已选择 <span class="batch-count">0</span> 个用例</span>
            <div>
                <button class="btn btn-sm btn-success" onclick="batchUpdateStatus('pass')">
                    <i class="fas fa-check me-1"></i>批量标记通过
                </button>
                <button class="btn btn-sm btn-danger" onclick="batchUpdateStatus('fail')">
                    <i class="fas fa-times me-1"></i>批量标记失败
                </button>
                <button class="btn btn-sm btn-secondary" onclick="clearSelection()">
                    <i class="fas fa-times me-1"></i>取消选择
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 用例列表 -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover searchable filterable">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAll" class="form-check-input">
                            </th>
                            <th>用例名称</th>
                            <th>测试分类</th>
                            <th>功能点</th>
                            <th>负责人</th>
                            <th>子系统状态</th>
                            <th>TOP状态</th>
                            <th>后仿状态</th>
                            <th>执行时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for testcase in page_obj %}
                            <tr data-status="{{ testcase.subsys_status }}" 
                                data-project="{{ testcase.testplan.project.id }}"
                                data-category="{{ testcase.test_category }}">
                                <td>
                                    <input type="checkbox" class="form-check-input item-checkbox" 
                                           value="{{ testcase.id }}">
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1">{{ testcase.testcase_name|truncatechars:40 }}</h6>
                                        <small class="text-muted">{{ testcase.testplan.project.name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ testcase.test_category }}</span>
                                </td>
                                <td>{{ testcase.function_points|truncatechars:30 }}</td>
                                <td>{{ testcase.owner }}</td>
                                <td>
                                    <span class="badge status-{{ testcase.subsys_status }}">
                                        {{ testcase.get_subsys_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge status-{{ testcase.top_status }}">
                                        {{ testcase.get_top_status_display }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="badge status-{{ testcase.post_subsys_status }} mb-1">
                                            子系统: {{ testcase.get_post_subsys_status_display }}
                                        </span>
                                        <span class="badge status-{{ testcase.post_top_status }}">
                                            TOP: {{ testcase.get_post_top_status_display }}
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    {% if testcase.start_time %}
                                        <small class="text-muted">
                                            开始: {{ testcase.start_time|date:"m-d H:i" }}<br>
                                            {% if testcase.end_time %}
                                                结束: {{ testcase.end_time|date:"m-d H:i" }}<br>
                                                耗时: {{ testcase.actual_time }}
                                            {% endif %}
                                        </small>
                                    {% else %}
                                        <span class="text-muted">未执行</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" 
                                                onclick="viewTestcaseDetail({{ testcase.id }})" 
                                                title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{% url 'dashboard:edit_testcase' testcase.id %}"
                                           class="btn btn-outline-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="btn btn-outline-success" 
                                                onclick="updateStatus({{ testcase.id }}, 'pass')" 
                                                title="标记通过">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" 
                                                onclick="updateStatus({{ testcase.id }}, 'fail')" 
                                                title="标记失败">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="用例分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无测试用例</h5>
                <p class="text-muted">请先上传测试计划文件</p>
                <a href="{% url 'dashboard:upload_testplan' %}" class="btn btn-primary">
                    <i class="fas fa-upload me-1"></i>上传测试计划
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- 用例详情模态框 -->
<div class="modal fade" id="testcaseDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">用例详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="testcaseDetailContent">
                <!-- 用例详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 过滤功能
function clearFilters() {
    $('.project-filter').val('');
    $('.status-filter').val('');
    $('.category-filter').val('');
    $('#searchInput').val('');
    $('.filterable tbody tr').show();
}

// 查看用例详情
function viewTestcaseDetail(testcaseId) {
    $('#testcaseDetailContent').html('<div class="text-center"><span class="loading"></span> 加载中...</div>');
    $('#testcaseDetailModal').modal('show');

    // 通过AJAX加载用例详情
    $.ajax({
        url: '/testcases/' + testcaseId + '/',
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            $('#testcaseDetailContent').html(response);
        },
        error: function(xhr, status, error) {
            $('#testcaseDetailContent').html(
                '<div class="alert alert-danger">加载失败，请稍后重试</div>'
            );
        }
    });
}

// 更新用例状态
function updateStatus(testcaseId, status) {
    if (confirm('确定要更新用例状态吗？')) {
        $.ajax({
            url: '/api/update_testcase_status/',
            type: 'POST',
            data: {
                'testcase_id': testcaseId,
                'status': status,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    showMessage('success', '状态更新成功');
                    // 刷新页面或更新表格行
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showMessage('danger', response.message || '状态更新失败');
                }
            },
            error: function(xhr, status, error) {
                showMessage('danger', '网络错误，请稍后重试');
            }
        });
    }
}

function showMessage(type, message) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>';

    // 移除现有的alert
    $('.alert').remove();

    // 添加新的alert到页面顶部
    $('.container-fluid').prepend(alertHtml);

    // 滚动到顶部
    $('html, body').animate({scrollTop: 0}, 500);

    // 5秒后自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// 批量更新状态
function batchUpdateStatus(status) {
    var selectedIds = [];
    $('.item-checkbox:checked').each(function() {
        selectedIds.push($(this).val());
    });
    
    if (selectedIds.length === 0) {
        alert('请先选择要更新的用例');
        return;
    }
    
    if (confirm('确定要批量更新 ' + selectedIds.length + ' 个用例的状态吗？')) {
        // 这里应该通过AJAX批量更新状态
        console.log('批量更新用例状态', selectedIds, status);
        // 实际实现时需要调用后端API
    }
}

// 清除选择
function clearSelection() {
    $('.item-checkbox').prop('checked', false);
    $('#selectAll').prop('checked', false);
    $('.batch-actions').hide();
}

$(document).ready(function() {
    // 多重过滤功能
    function applyFilters() {
        var projectFilter = $('.project-filter').val();
        var statusFilter = $('.status-filter').val();
        var categoryFilter = $('.category-filter').val();
        var searchText = $('#searchInput').val().toLowerCase();
        
        $('.filterable tbody tr').each(function() {
            var row = $(this);
            var show = true;
            
            // 项目过滤
            if (projectFilter && row.data('project') != projectFilter) {
                show = false;
            }
            
            // 状态过滤
            if (statusFilter && row.data('status') != statusFilter) {
                show = false;
            }
            
            // 分类过滤
            if (categoryFilter && row.data('category') != categoryFilter) {
                show = false;
            }
            
            // 搜索过滤
            if (searchText && row.text().toLowerCase().indexOf(searchText) === -1) {
                show = false;
            }
            
            row.toggle(show);
        });
    }
    
    // 绑定过滤事件
    $('.project-filter, .status-filter, .category-filter').change(applyFilters);
    $('#searchInput').on('keyup', applyFilters);
});
</script>
{% endblock %}
