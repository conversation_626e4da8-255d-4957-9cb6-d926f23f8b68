"""
简化的初始化数据脚本
"""
import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
django.setup()

from django.contrib.auth.models import User
from dashboard.models import Project

def create_admin_user():
    """创建管理员用户"""
    try:
        if not User.objects.filter(username='admin').exists():
            User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            print("✓ 创建管理员用户成功")
            print("  用户名: admin")
            print("  密码: admin123")
            print("  邮箱: <EMAIL>")
        else:
            print("✓ 管理员用户已存在")
            print("  用户名: admin")
            print("  密码: admin123")
    except Exception as e:
        print(f"✗ 创建管理员用户失败: {e}")

def create_test_users():
    """创建测试用户"""
    test_users = [
        {'username': 'user1', 'email': '<EMAIL>', 'password': 'user123'},
        {'username': 'user2', 'email': '<EMAIL>', 'password': 'user123'},
    ]

    for user_data in test_users:
        try:
            if not User.objects.filter(username=user_data['username']).exists():
                User.objects.create_user(**user_data)
                print(f"✓ 创建用户: {user_data['username']}/user123")
            else:
                print(f"✓ 用户已存在: {user_data['username']}/user123")
        except Exception as e:
            print(f"✗ 创建用户失败: {e}")

def create_test_project():
    """创建测试项目"""
    try:
        admin_user = User.objects.get(username='admin')
        if not Project.objects.filter(name='测试项目').exists():
            Project.objects.create(
                name='测试项目',
                description='这是一个测试项目',
                owner=admin_user
            )
            print("✓ 创建测试项目成功")
        else:
            print("✓ 测试项目已存在")
    except Exception as e:
        print(f"✗ 创建测试项目失败: {e}")

def reset_admin_password():
    """重置管理员密码"""
    try:
        admin_user = User.objects.get(username='admin')
        admin_user.set_password('admin123')
        admin_user.save()
        print("✓ 管理员密码已重置为: admin123")
    except User.DoesNotExist:
        print("✗ 管理员用户不存在")
    except Exception as e:
        print(f"✗ 重置密码失败: {e}")

if __name__ == "__main__":
    print("=== RunSim 仪表盘初始化 ===")

    create_admin_user()
    create_test_users()
    create_test_project()

    print("\n=== 登录信息 ===")
    print("管理员账户:")
    print("  用户名: admin")
    print("  密码: admin123")
    print("  权限: 超级管理员")
    print("\n普通用户账户:")
    print("  用户名: user1 / 密码: user123")
    print("  用户名: user2 / 密码: user123")

    print("\n=== 访问地址 ===")
    print("仪表盘: http://localhost:8000/")
    print("管理后台: http://localhost:8000/admin/")

    print("\n=== 重置管理员密码 ===")
    print("如需重置管理员密码，请运行:")
    print("python -c \"import init_data; init_data.reset_admin_password()\"")

    print("\n✅ 初始化完成！")
