{% extends 'dashboard/base.html' %}

{% block title %}BUG管理 - RunSim 仪表盘系统{% endblock %}

{% csrf_token %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-bug me-2"></i>BUG管理</h2>
    <div>
        <a href="{% url 'dashboard:create_bug' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>新建BUG
        </a>
        <button class="btn btn-success export-btn" data-format="excel" data-url="/api/export/bugs/">
            <i class="fas fa-file-excel me-1"></i>导出Excel
        </button>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ page_obj.paginator.count }}</h4>
                        <p class="mb-0">总BUG数</p>
                    </div>
                    <i class="fas fa-bug fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="openBugCount">-</h4>
                        <p class="mb-0">待处理</p>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="resolvedBugCount">-</h4>
                        <p class="mb-0">已解决</p>
                    </div>
                    <i class="fas fa-check-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="weeklyBugCount">-</h4>
                        <p class="mb-0">本周新增</p>
                    </div>
                    <i class="fas fa-calendar-week fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- BUG趋势图 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>BUG趋势分析
                </h5>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary active" id="bugWeeklyViewBtn" onclick="switchBugTrendView('weekly')">
                        <i class="fas fa-calendar-week me-1"></i>按周
                    </button>
                    <button type="button" class="btn btn-outline-primary" id="bugDailyViewBtn" onclick="switchBugTrendView('daily')">
                        <i class="fas fa-calendar-day me-1"></i>按日
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="toggleBugChartType()">
                        <i class="fas fa-chart-bar me-1"></i><span id="bugChartTypeText">柱状图</span>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">总BUG数</h6>
                            <h4 class="text-danger" id="bugTotalCount">-</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">已解决</h6>
                            <h4 class="text-success" id="bugResolvedCount">-</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">待处理</h6>
                            <h4 class="text-warning" id="bugOpenCount">-</h4>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h6 class="text-muted">解决率</h6>
                            <h4 class="text-info" id="bugResolutionRate">-</h4>
                        </div>
                    </div>
                </div>
                <div style="height: 400px;">
                    <canvas id="bugTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-3">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="搜索BUG ID或标题...">
            </div>
        </div>
        <div class="col-md-2">
            <select class="form-select project-filter">
                <option value="">所有项目</option>
                {% for project in projects %}
                    <option value="{{ project.id }}" {% if current_project == project.id|stringformat:"s" %}selected{% endif %}>
                        {{ project.name }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select status-filter">
                <option value="">所有状态</option>
                {% for value, display in status_choices %}
                    <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>
                        {{ display }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select type-filter">
                <option value="">所有类型</option>
                {% for value, display in type_choices %}
                    <option value="{{ value }}" {% if current_type == value %}selected{% endif %}>
                        {{ display }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-2">
            <select class="form-select severity-filter">
                <option value="">所有严重程度</option>
                <option value="critical">严重</option>
                <option value="major">主要</option>
                <option value="minor">次要</option>
                <option value="trivial">轻微</option>
            </select>
        </div>
        <div class="col-md-1">
            <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<!-- BUG列表 -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover searchable filterable">
                    <thead>
                        <tr>
                            <th>BUG ID</th>
                            <th>标题</th>
                            <th>项目</th>
                            <th>类型</th>
                            <th>严重程度</th>
                            <th>状态</th>
                            <th>提交者</th>
                            <th>验证人</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for bug in page_obj %}
                            <tr data-status="{{ bug.status }}" 
                                data-project="{{ bug.project.id }}"
                                data-type="{{ bug.bug_type }}"
                                data-severity="{{ bug.severity }}">
                                <td>
                                    <strong class="text-primary">{{ bug.bug_id }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <h6 class="mb-1">{{ bug.title|truncatechars:40 }}</h6>
                                        <small class="text-muted">{{ bug.description|truncatechars:60 }}</small>
                                    </div>
                                </td>
                                <td>{{ bug.project.name }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ bug.get_bug_type_display }}</span>
                                </td>
                                <td>
                                    {% if bug.severity == 'critical' %}
                                        <span class="badge bg-danger">{{ bug.get_severity_display }}</span>
                                    {% elif bug.severity == 'major' %}
                                        <span class="badge bg-warning">{{ bug.get_severity_display }}</span>
                                    {% elif bug.severity == 'minor' %}
                                        <span class="badge bg-info">{{ bug.get_severity_display }}</span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">{{ bug.get_severity_display }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if bug.status == 'open' %}
                                        <span class="badge bg-danger">{{ bug.get_status_display }}</span>
                                    {% elif bug.status == 'in_progress' %}
                                        <span class="badge bg-warning">{{ bug.get_status_display }}</span>
                                    {% elif bug.status == 'resolved' %}
                                        <span class="badge bg-success">{{ bug.get_status_display }}</span>
                                    {% elif bug.status == 'closed' %}
                                        <span class="badge bg-secondary">{{ bug.get_status_display }}</span>
                                    {% else %}
                                        <span class="badge bg-dark">{{ bug.get_status_display }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ bug.submitter.username }}</td>
                                <td>
                                    {% if bug.assignee %}
                                        {{ bug.assignee.username }}
                                    {% else %}
                                        <span class="text-muted">未分配</span>
                                    {% endif %}
                                </td>
                                <td>{{ bug.created_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" 
                                                onclick="viewBugDetail({{ bug.id }})" 
                                                title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{% url 'dashboard:edit_bug' bug.id %}"
                                           class="btn btn-outline-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if bug.status == 'open' %}
                                            <button class="btn btn-outline-warning" 
                                                    onclick="updateBugStatus({{ bug.id }}, 'in_progress')" 
                                                    title="开始处理">
                                                <i class="fas fa-play"></i>
                                            </button>
                                        {% elif bug.status == 'in_progress' %}
                                            <button class="btn btn-outline-success" 
                                                    onclick="updateBugStatus({{ bug.id }}, 'resolved')" 
                                                    title="标记解决">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="BUG分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if current_project %}&project={{ current_project }}{% endif %}{% if current_status %}&status={{ current_status }}{% endif %}{% if current_type %}&type={{ current_type }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-bug fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无BUG记录</h5>
                <p class="text-muted">点击上方"新建BUG"按钮创建第一个BUG记录</p>
                <a href="{% url 'dashboard:create_bug' %}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>新建BUG
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- BUG详情模态框 -->
<div class="modal fade" id="bugDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">BUG详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="bugDetailContent">
                <!-- BUG详情内容将通过AJAX加载 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 过滤功能
function clearFilters() {
    $('.project-filter').val('');
    $('.status-filter').val('');
    $('.type-filter').val('');
    $('.severity-filter').val('');
    $('#searchInput').val('');
    $('.filterable tbody tr').show();
}

// 查看BUG详情
function viewBugDetail(bugId) {
    $('#bugDetailContent').html('<div class="text-center"><span class="loading"></span> 加载中...</div>');
    $('#bugDetailModal').modal('show');

    // 通过AJAX加载BUG详情
    $.ajax({
        url: '/bugs/' + bugId + '/',
        type: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        },
        success: function(response) {
            $('#bugDetailContent').html(response);
        },
        error: function(xhr, status, error) {
            $('#bugDetailContent').html(
                '<div class="alert alert-danger">加载失败，请稍后重试</div>'
            );
        }
    });
}

// 更新BUG状态
function updateBugStatus(bugId, status) {
    var statusText = {
        'in_progress': '处理中',
        'resolved': '已解决',
        'closed': '已关闭'
    };

    if (confirm('确定要将BUG状态更新为"' + statusText[status] + '"吗？')) {
        $.ajax({
            url: '/api/update_bug_status/',
            type: 'POST',
            data: {
                'bug_id': bugId,
                'status': status,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    showMessage('success', '状态更新成功');
                    // 刷新页面
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    showMessage('danger', response.message || '状态更新失败');
                }
            },
            error: function(xhr, status, error) {
                showMessage('danger', '网络错误，请稍后重试');
            }
        });
    }
}

function showMessage(type, message) {
    var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                   message +
                   '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                   '</div>';

    // 移除现有的alert
    $('.alert').remove();

    // 添加新的alert到页面顶部
    $('.container-fluid').prepend(alertHtml);

    // 滚动到顶部
    $('html, body').animate({scrollTop: 0}, 500);

    // 5秒后自动隐藏
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

// BUG趋势图相关变量
let bugTrendChart = null;
let currentBugTrendView = 'weekly';
let currentBugChartType = 'line';
let bugTrendData = null;

// 初始化BUG趋势图
function initBugTrendChart() {
    loadBugTrendData();
}

// 加载BUG趋势数据
function loadBugTrendData() {
    const projectFilter = $('.project-filter').val();
    let url = '/api/bug_stats/';

    if (projectFilter) {
        url += '?project=' + projectFilter;
    }

    $.ajax({
        url: url,
        type: 'GET',
        success: function(response) {
            if (response.success) {
                bugTrendData = response.data;
                updateBugSummaryStats();
                renderBugTrendChart();
            } else {
                console.error('加载BUG趋势数据失败:', response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('加载BUG趋势数据失败:', error);
        }
    });
}

// 更新BUG汇总统计
function updateBugSummaryStats() {
    if (bugTrendData && bugTrendData.summary) {
        $('#bugTotalCount').text(bugTrendData.summary.total_bugs);
        $('#bugResolvedCount').text(bugTrendData.summary.total_resolved);
        $('#bugOpenCount').text(bugTrendData.summary.total_open);
        $('#bugResolutionRate').text(bugTrendData.summary.resolution_rate + '%');
    }
}

// 渲染BUG趋势图
function renderBugTrendChart() {
    if (!bugTrendData) return;

    const ctx = document.getElementById('bugTrendChart').getContext('2d');

    // 销毁现有图表
    if (bugTrendChart) {
        bugTrendChart.destroy();
    }

    const data = currentBugTrendView === 'weekly' ? bugTrendData.weekly_stats : bugTrendData.daily_stats;
    const labels = data.map(item => currentBugTrendView === 'weekly' ? item.week_label : item.date_label);
    const createdData = data.map(item => item.created);
    const resolvedData = data.map(item => item.resolved);
    const cumulativeCreatedData = data.map(item => item.cumulative_created);
    const cumulativeResolvedData = data.map(item => item.cumulative_resolved);

    const config = {
        type: currentBugChartType,
        data: {
            labels: labels,
            datasets: [
                {
                    label: currentBugTrendView === 'weekly' ? '每周新增BUG' : '每日新增BUG',
                    data: createdData,
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                },
                {
                    label: currentBugTrendView === 'weekly' ? '每周解决BUG' : '每日解决BUG',
                    data: resolvedData,
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                },
                {
                    label: '累计新增',
                    data: cumulativeCreatedData,
                    borderColor: 'rgb(255, 159, 64)',
                    backgroundColor: 'rgba(255, 159, 64, 0.2)',
                    tension: 0.1,
                    yAxisID: 'y1'
                },
                {
                    label: '累计解决',
                    data: cumulativeResolvedData,
                    borderColor: 'rgb(54, 162, 235)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    tension: 0.1,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            scales: {
                x: {
                    display: true,
                    title: {
                        display: true,
                        text: currentBugTrendView === 'weekly' ? '周' : '日期'
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: '新增/解决数量'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: '累计数量'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            plugins: {
                title: {
                    display: true,
                    text: currentBugTrendView === 'weekly' ? 'BUG趋势分析（按周）' : 'BUG趋势分析（按日）'
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        afterLabel: function(context) {
                            const datasetLabel = context.dataset.label;
                            if (datasetLabel.includes('新增') || datasetLabel.includes('解决')) {
                                return datasetLabel + ': ' + context.parsed.y + ' 个';
                            } else {
                                return datasetLabel + ': ' + context.parsed.y + ' 个';
                            }
                        }
                    }
                }
            }
        }
    };

    bugTrendChart = new Chart(ctx, config);
}

// 切换BUG趋势视图（按周/按日）
function switchBugTrendView(view) {
    currentBugTrendView = view;

    // 更新按钮状态
    $('#bugWeeklyViewBtn, #bugDailyViewBtn').removeClass('active');
    if (view === 'weekly') {
        $('#bugWeeklyViewBtn').addClass('active');
    } else {
        $('#bugDailyViewBtn').addClass('active');
    }

    // 重新渲染图表
    renderBugTrendChart();
}

// 切换BUG图表类型（折线图/柱状图）
function toggleBugChartType() {
    currentBugChartType = currentBugChartType === 'line' ? 'bar' : 'line';

    // 更新按钮文本
    $('#bugChartTypeText').text(currentBugChartType === 'line' ? '柱状图' : '折线图');

    // 重新渲染图表
    renderBugTrendChart();
}

$(document).ready(function() {
    // 初始化BUG趋势图
    initBugTrendChart();

    // 计算统计数据
    function updateStatistics() {
        var openCount = 0;
        var resolvedCount = 0;
        var weeklyCount = 0;
        var oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

        $('.filterable tbody tr:visible').each(function() {
            var status = $(this).data('status');
            if (status === 'open' || status === 'in_progress') {
                openCount++;
            }
            if (status === 'resolved' || status === 'closed') {
                resolvedCount++;
            }
            // 这里应该根据实际的创建时间计算本周新增
            weeklyCount++;
        });

        $('#openBugCount').text(openCount);
        $('#resolvedBugCount').text(resolvedCount);
        $('#weeklyBugCount').text(Math.floor(weeklyCount / 7)); // 简化计算
    }
    
    // 多重过滤功能
    function applyFilters() {
        var projectFilter = $('.project-filter').val();
        var statusFilter = $('.status-filter').val();
        var typeFilter = $('.type-filter').val();
        var severityFilter = $('.severity-filter').val();
        var searchText = $('#searchInput').val().toLowerCase();
        
        $('.filterable tbody tr').each(function() {
            var row = $(this);
            var show = true;
            
            // 项目过滤
            if (projectFilter && row.data('project') != projectFilter) {
                show = false;
            }
            
            // 状态过滤
            if (statusFilter && row.data('status') != statusFilter) {
                show = false;
            }
            
            // 类型过滤
            if (typeFilter && row.data('type') != typeFilter) {
                show = false;
            }
            
            // 严重程度过滤
            if (severityFilter && row.data('severity') != severityFilter) {
                show = false;
            }
            
            // 搜索过滤
            if (searchText && row.text().toLowerCase().indexOf(searchText) === -1) {
                show = false;
            }
            
            row.toggle(show);
        });
        
        updateStatistics();
    }
    
    // 绑定过滤事件
    $('.project-filter, .status-filter, .type-filter, .severity-filter').change(function() {
        applyFilters();
        // 项目过滤变化时重新加载BUG趋势数据
        if ($(this).hasClass('project-filter')) {
            loadBugTrendData();
        }
    });
    $('#searchInput').on('keyup', applyFilters);
    
    // 初始化统计
    updateStatistics();
});
</script>
{% endblock %}
