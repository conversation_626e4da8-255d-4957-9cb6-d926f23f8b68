from django.urls import path
from . import views

app_name = 'dashboard'

urlpatterns = [
    # 仪表盘首页
    path('', views.dashboard_home, name='home'),
    
    # 项目管理
    path('projects/', views.project_list, name='project_list'),
    path('projects/<int:project_id>/', views.project_detail, name='project_detail'),
    
    # 用例管理
    path('testcases/', views.testcase_list, name='testcase_list'),
    path('testcases/<int:testcase_id>/', views.testcase_detail, name='testcase_detail'),
    path('testcases/<int:testcase_id>/edit/', views.edit_testcase, name='edit_testcase'),
    
    # BUG管理
    path('bugs/', views.bug_list, name='bug_list'),
    path('bugs/create/', views.create_bug, name='create_bug'),
    path('bugs/<int:bug_id>/', views.bug_detail, name='bug_detail'),
    path('bugs/<int:bug_id>/edit/', views.edit_bug, name='edit_bug'),
    
    # API接口
    path('api/update_testcase_status/', views.update_testcase_status, name='update_testcase_status'),
    path('api/update_bug_status/', views.update_bug_status, name='update_bug_status'),
    path('api/execution_stats/', views.execution_stats, name='execution_stats'),
    path('api/bug_stats/', views.bug_stats, name='bug_stats'),

    # RunSim集成API
    path('api/runsim/execution/start/', views.runsim_execution_start, name='runsim_execution_start'),
    path('api/runsim/execution/complete/', views.runsim_execution_complete, name='runsim_execution_complete'),
    path('api/runsim/status/', views.runsim_integration_status, name='runsim_integration_status'),

    # 导出功能
    path('api/export/testcases/', views.export_testcases, name='export_testcases'),
    path('api/export/bugs/', views.export_bugs, name='export_bugs'),

    # 上传功能
    path('upload/testplan/', views.upload_testplan, name='upload_testplan'),
]
