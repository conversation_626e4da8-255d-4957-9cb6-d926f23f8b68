#!/usr/bin/env python
"""
创建测试数据的脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'dashboard_project.settings')
django.setup()

from django.contrib.auth.models import User
from dashboard.models import Project, TestPlan, TestCase, Bug, ExecutionRecord, CaseStatusSummary


def create_superuser():
    """创建超级用户"""
    if not User.objects.filter(username='admin').exists():
        User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print("✓ 创建超级用户: admin/admin123")
    else:
        print("✓ 超级用户已存在")


def create_test_users():
    """创建测试用户"""
    users_data = [
        {'username': 'tester1', 'email': '<EMAIL>', 'password': 'test123'},
        {'username': 'tester2', 'email': '<EMAIL>', 'password': 'test123'},
        {'username': 'developer1', 'email': '<EMAIL>', 'password': 'dev123'},
    ]
    
    for user_data in users_data:
        if not User.objects.filter(username=user_data['username']).exists():
            User.objects.create_user(**user_data)
            print(f"✓ 创建用户: {user_data['username']}")


def create_test_projects():
    """创建测试项目"""
    admin_user = User.objects.get(username='admin')
    
    projects_data = [
        {
            'name': 'SOC芯片验证项目',
            'description': '主要SOC芯片的功能验证项目，包括CPU、GPU、内存控制器等模块的验证',
            'current_soc_phase': 'rtl_0_5',
            'current_verification_phase': 'dvr2',
            'owner': admin_user
        },
        {
            'name': '5G基带芯片项目',
            'description': '5G基带芯片的验证项目，重点验证物理层和协议栈功能',
            'current_soc_phase': 'rtl_0_9',
            'current_verification_phase': 'dvr3',
            'owner': admin_user
        },
        {
            'name': 'AI加速器验证',
            'description': 'AI加速器芯片的验证项目，包括神经网络处理单元的验证',
            'current_soc_phase': 'pre_rtl_0_9',
            'current_verification_phase': 'dvr1',
            'owner': admin_user
        }
    ]
    
    for project_data in projects_data:
        project, created = Project.objects.get_or_create(
            name=project_data['name'],
            defaults=project_data
        )
        if created:
            print(f"✓ 创建项目: {project.name}")


def create_test_testcases():
    """创建测试用例"""
    projects = Project.objects.all()
    
    for project in projects:
        # 创建测试计划
        testplan, created = TestPlan.objects.get_or_create(
            project=project,
            name=f"{project.name}_测试计划",
            defaults={
                'subsystem_name': 'CPU子系统',
            }
        )
        if created:
            print(f"✓ 创建测试计划: {testplan.name}")
        
        # 创建测试用例
        testcases_data = [
            {
                'test_category': '功能测试',
                'items': 'TC001',
                'test_areas': 'CPU核心',
                'function_points': '指令执行',
                'test_scope': '验证CPU基本指令执行功能',
                'check_point': '指令执行结果正确',
                'cover': '指令覆盖率',
                'testcase_name': 'cpu_basic_instruction_test',
                'owner': 'tester1',
                'subsys_status': 'pass',
                'top_status': 'on_going',
            },
            {
                'test_category': '性能测试',
                'items': 'TC002',
                'test_areas': '内存控制器',
                'function_points': '内存访问',
                'test_scope': '验证内存控制器性能',
                'check_point': '内存访问延迟符合要求',
                'cover': '内存访问模式覆盖',
                'testcase_name': 'memory_controller_performance_test',
                'owner': 'tester2',
                'subsys_status': 'on_going',
                'top_status': 'not_started',
            },
            {
                'test_category': '接口测试',
                'items': 'TC003',
                'test_areas': 'PCIe接口',
                'function_points': 'PCIe通信',
                'test_scope': '验证PCIe接口通信功能',
                'check_point': 'PCIe数据传输正确',
                'cover': 'PCIe协议覆盖',
                'testcase_name': 'pcie_interface_test',
                'owner': 'tester1',
                'subsys_status': 'fail',
                'top_status': 'not_started',
            },
            {
                'test_category': '压力测试',
                'items': 'TC004',
                'test_areas': '整体系统',
                'function_points': '系统稳定性',
                'test_scope': '验证系统在高负载下的稳定性',
                'check_point': '系统无崩溃',
                'cover': '负载场景覆盖',
                'testcase_name': 'system_stress_test',
                'owner': 'tester2',
                'subsys_status': 'not_started',
                'top_status': 'not_started',
            }
        ]
        
        for tc_data in testcases_data:
            testcase, created = TestCase.objects.get_or_create(
                testplan=testplan,
                testcase_name=tc_data['testcase_name'],
                defaults=tc_data
            )
            if created:
                print(f"✓ 创建测试用例: {testcase.testcase_name}")


def create_test_bugs():
    """创建测试BUG"""
    projects = Project.objects.all()
    users = User.objects.all()
    
    bugs_data = [
        {
            'bug_id': 'BUG-001',
            'title': 'CPU指令执行错误',
            'description': '在执行特定指令序列时，CPU返回错误结果',
            'bug_type': 'functional',
            'severity': 'major',
            'verification_phase': 'dvr2',
            'discovered_platform': 'FPGA验证平台',
            'discovered_testcase': 'cpu_basic_instruction_test',
            'status': 'open',
        },
        {
            'bug_id': 'BUG-002',
            'title': '内存控制器性能不达标',
            'description': '内存访问延迟超过设计要求',
            'bug_type': 'performance',
            'severity': 'minor',
            'verification_phase': 'dvr2',
            'discovered_platform': '仿真平台',
            'discovered_testcase': 'memory_controller_performance_test',
            'status': 'in_progress',
        },
        {
            'bug_id': 'BUG-003',
            'title': 'PCIe接口数据丢失',
            'description': '在高速数据传输时偶现数据丢失',
            'bug_type': 'interface',
            'severity': 'critical',
            'verification_phase': 'dvr3',
            'discovered_platform': '硬件验证平台',
            'discovered_testcase': 'pcie_interface_test',
            'status': 'resolved',
        }
    ]
    
    for i, bug_data in enumerate(bugs_data):
        project = projects[i % len(projects)]
        submitter = users[i % len(users)]
        assignee = users[(i + 1) % len(users)]
        
        bug, created = Bug.objects.get_or_create(
            bug_id=bug_data['bug_id'],
            defaults={
                **bug_data,
                'project': project,
                'submitter': submitter,
                'assignee': assignee,
            }
        )
        if created:
            print(f"✓ 创建BUG: {bug.bug_id}")


def create_execution_records():
    """创建执行记录"""
    testcases = TestCase.objects.all()
    users = User.objects.all()
    
    for i, testcase in enumerate(testcases[:5]):  # 只为前5个用例创建执行记录
        start_time = datetime.now() - timedelta(days=i+1, hours=i)
        end_time = start_time + timedelta(hours=2, minutes=30)
        
        execution, created = ExecutionRecord.objects.get_or_create(
            testcase=testcase,
            start_time=start_time,
            defaults={
                'command': f'runsim -case {testcase.testcase_name} -base top',
                'case_param': testcase.testcase_name,
                'base_param': 'top',
                'result': ['pass', 'fail', 'pass', 'pass', 'fail'][i],
                'end_time': end_time,
                'duration': end_time - start_time,
                'executor': users[i % len(users)],
            }
        )
        if created:
            print(f"✓ 创建执行记录: {testcase.testcase_name}")


def update_case_status_summary():
    """更新用例状态汇总"""
    testplans = TestPlan.objects.all()
    
    for testplan in testplans:
        testcases = TestCase.objects.filter(testplan=testplan)
        
        summary, created = CaseStatusSummary.objects.get_or_create(
            testplan=testplan,
            defaults={'total_cases': testcases.count()}
        )
        
        # 更新统计数据
        summary.total_cases = testcases.count()
        summary.subsys_pass = testcases.filter(subsys_status='pass').count()
        summary.subsys_fail = testcases.filter(subsys_status='fail').count()
        summary.subsys_on_going = testcases.filter(subsys_status='on_going').count()
        summary.subsys_not_started = testcases.filter(subsys_status='not_started').count()
        
        summary.top_pass = testcases.filter(top_status='pass').count()
        summary.top_fail = testcases.filter(top_status='fail').count()
        summary.top_on_going = testcases.filter(top_status='on_going').count()
        summary.top_not_started = testcases.filter(top_status='not_started').count()
        
        summary.save()
        
        if created:
            print(f"✓ 创建状态汇总: {testplan.name}")


def main():
    """主函数"""
    print("开始创建测试数据...")
    
    create_superuser()
    create_test_users()
    create_test_projects()
    create_test_testcases()
    create_test_bugs()
    create_execution_records()
    update_case_status_summary()
    
    print("\n✅ 测试数据创建完成！")
    print("\n登录信息:")
    print("管理员: admin / admin123")
    print("测试用户: tester1 / test123")
    print("测试用户: tester2 / test123")
    print("开发用户: developer1 / dev123")
    print("\n访问地址:")
    print("仪表盘: http://localhost:8000/")
    print("管理后台: http://localhost:8000/admin/")


if __name__ == '__main__':
    main()
