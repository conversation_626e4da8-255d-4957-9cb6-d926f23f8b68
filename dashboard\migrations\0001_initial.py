# Generated by Django 5.2.2 on 2025-06-05 08:52

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TestCase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('test_category', models.CharField(max_length=100, verbose_name='测试分类')),
                ('items', models.CharField(max_length=100, verbose_name='测试项')),
                ('test_areas', models.CharField(max_length=200, verbose_name='测试区域')),
                ('function_points', models.CharField(max_length=200, verbose_name='功能点')),
                ('test_scope', models.TextField(verbose_name='测试范围')),
                ('check_point', models.TextField(verbose_name='检查点')),
                ('cover', models.CharField(max_length=200, verbose_name='覆盖')),
                ('testcase_name', models.CharField(max_length=200, verbose_name='测试用例名称')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('actual_time', models.DurationField(blank=True, null=True, verbose_name='实际时间')),
                ('owner', models.CharField(max_length=100, verbose_name='负责人')),
                ('subsys_phase', models.CharField(blank=True, max_length=50, verbose_name='子系统阶段')),
                ('subsys_status', models.CharField(choices=[('not_started', '未开始'), ('on_going', '进行中'), ('pass', '通过'), ('fail', '失败'), ('skip', '跳过')], default='not_started', max_length=20, verbose_name='子系统状态')),
                ('top_phase', models.CharField(blank=True, max_length=50, verbose_name='TOP阶段')),
                ('top_status', models.CharField(choices=[('not_started', '未开始'), ('on_going', '进行中'), ('pass', '通过'), ('fail', '失败'), ('skip', '跳过')], default='not_started', max_length=20, verbose_name='TOP状态')),
                ('post_subsys_phase', models.CharField(blank=True, max_length=50, verbose_name='后仿子系统阶段')),
                ('post_subsys_status', models.CharField(choices=[('not_started', '未开始'), ('on_going', '进行中'), ('pass', '通过'), ('fail', '失败'), ('skip', '跳过')], default='not_started', max_length=20, verbose_name='后仿子系统状态')),
                ('post_top_phase', models.CharField(blank=True, max_length=50, verbose_name='后仿TOP阶段')),
                ('post_top_status', models.CharField(choices=[('not_started', '未开始'), ('on_going', '进行中'), ('pass', '通过'), ('fail', '失败'), ('skip', '跳过')], default='not_started', max_length=20, verbose_name='后仿TOP状态')),
                ('note', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '测试用例',
                'verbose_name_plural': '测试用例',
                'ordering': ['testplan', 'items'],
            },
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='项目名称')),
                ('description', models.TextField(blank=True, verbose_name='项目描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('current_soc_phase', models.CharField(choices=[('kickoff', 'Kickoff'), ('pre_rtl_0_1', 'PreRTL0.1'), ('rtl_0_1', 'RTL0.1'), ('pre_rtl_0_5', 'PreRTL0.5'), ('rtl_0_5', 'RTL0.5'), ('pre_rtl_0_9', 'PreRTL0.9'), ('rtl_0_9', 'RTL0.9'), ('post_sim', '后仿')], default='kickoff', max_length=20, verbose_name='当前SOC阶段')),
                ('current_verification_phase', models.CharField(choices=[('dvr1', 'DVR1'), ('dvr2', 'DVR2'), ('dvr3', 'DVR3'), ('dvs1', 'DVS1'), ('dvs2', 'DVS2')], default='dvr1', max_length=20, verbose_name='当前验证阶段')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='负责人')),
            ],
            options={
                'verbose_name': '项目',
                'verbose_name_plural': '项目',
                'ordering': ['-updated_at'],
            },
        ),
        migrations.CreateModel(
            name='Bug',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bug_id', models.CharField(max_length=50, unique=True, verbose_name='BUG ID')),
                ('title', models.CharField(max_length=200, verbose_name='BUG标题')),
                ('description', models.TextField(verbose_name='问题描述')),
                ('bug_type', models.CharField(choices=[('functional', '功能性'), ('performance', '性能'), ('interface', '接口'), ('timing', '时序'), ('power', '功耗'), ('other', '其他')], max_length=20, verbose_name='BUG类型')),
                ('severity', models.CharField(choices=[('critical', '严重'), ('major', '主要'), ('minor', '次要'), ('trivial', '轻微')], max_length=20, verbose_name='严重程度')),
                ('verification_phase', models.CharField(choices=[('dvr1', 'DVR1'), ('dvr2', 'DVR2'), ('dvr3', 'DVR3'), ('dvs1', 'DVS1'), ('dvs2', 'DVS2')], max_length=20, verbose_name='验证阶段')),
                ('discovered_platform', models.CharField(max_length=100, verbose_name='发现平台')),
                ('discovered_testcase', models.CharField(max_length=200, verbose_name='发现用例')),
                ('status', models.CharField(choices=[('open', '打开'), ('in_progress', '处理中'), ('resolved', '已解决'), ('closed', '已关闭'), ('rejected', '已拒绝')], default='open', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='解决时间')),
                ('assignee', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_bugs', to=settings.AUTH_USER_MODEL, verbose_name='验证人')),
                ('submitter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submitted_bugs', to=settings.AUTH_USER_MODEL, verbose_name='提交者')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.project', verbose_name='所属项目')),
            ],
            options={
                'verbose_name': 'BUG',
                'verbose_name_plural': 'BUG',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExecutionRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command', models.TextField(verbose_name='执行命令')),
                ('case_param', models.CharField(max_length=200, verbose_name='case参数')),
                ('base_param', models.CharField(blank=True, max_length=100, verbose_name='base参数')),
                ('block_param', models.CharField(blank=True, max_length=100, verbose_name='block参数')),
                ('post_param', models.CharField(blank=True, max_length=100, verbose_name='post参数')),
                ('result', models.CharField(choices=[('running', '运行中'), ('pass', '通过'), ('fail', '失败'), ('timeout', '超时'), ('error', '错误')], default='running', max_length=20, verbose_name='执行结果')),
                ('start_time', models.DateTimeField(verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('duration', models.DurationField(blank=True, null=True, verbose_name='执行时长')),
                ('log_file_path', models.CharField(blank=True, max_length=500, verbose_name='日志文件路径')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('executor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='执行者')),
                ('testcase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.testcase', verbose_name='测试用例')),
            ],
            options={
                'verbose_name': '执行记录',
                'verbose_name_plural': '执行记录',
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='TestPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='测试计划名称')),
                ('subsystem_name', models.CharField(max_length=200, verbose_name='子系统名称')),
                ('excel_file', models.FileField(upload_to='testplans/', verbose_name='Excel文件')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.project', verbose_name='所属项目')),
            ],
            options={
                'verbose_name': '测试计划',
                'verbose_name_plural': '测试计划',
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.AddField(
            model_name='testcase',
            name='testplan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.testplan', verbose_name='所属测试计划'),
        ),
        migrations.CreateModel(
            name='CaseStatusSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_cases', models.IntegerField(default=0, verbose_name='总用例数')),
                ('subsys_not_started', models.IntegerField(default=0, verbose_name='子系统未开始')),
                ('subsys_on_going', models.IntegerField(default=0, verbose_name='子系统进行中')),
                ('subsys_pass', models.IntegerField(default=0, verbose_name='子系统通过')),
                ('subsys_fail', models.IntegerField(default=0, verbose_name='子系统失败')),
                ('top_not_started', models.IntegerField(default=0, verbose_name='TOP未开始')),
                ('top_on_going', models.IntegerField(default=0, verbose_name='TOP进行中')),
                ('top_pass', models.IntegerField(default=0, verbose_name='TOP通过')),
                ('top_fail', models.IntegerField(default=0, verbose_name='TOP失败')),
                ('post_subsys_not_started', models.IntegerField(default=0, verbose_name='后仿子系统未开始')),
                ('post_subsys_on_going', models.IntegerField(default=0, verbose_name='后仿子系统进行中')),
                ('post_subsys_pass', models.IntegerField(default=0, verbose_name='后仿子系统通过')),
                ('post_subsys_fail', models.IntegerField(default=0, verbose_name='后仿子系统失败')),
                ('post_top_not_started', models.IntegerField(default=0, verbose_name='后仿TOP未开始')),
                ('post_top_on_going', models.IntegerField(default=0, verbose_name='后仿TOP进行中')),
                ('post_top_pass', models.IntegerField(default=0, verbose_name='后仿TOP通过')),
                ('post_top_fail', models.IntegerField(default=0, verbose_name='后仿TOP失败')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('testplan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='dashboard.testplan', verbose_name='所属测试计划')),
            ],
            options={
                'verbose_name': '用例状态汇总',
                'verbose_name_plural': '用例状态汇总',
                'unique_together': {('testplan',)},
            },
        ),
    ]
