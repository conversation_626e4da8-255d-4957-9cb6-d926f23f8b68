"""
执行面板视图组件
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QTabWidget, QMenu
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QIcon
from views.log_panel import LogPanel

class ExecutionPanel(QWidget):
    """执行面板，用于管理和显示执行日志"""

    # 定义信号
    open_verdi_requested = pyqtSignal(str)
    open_verisium_requested = pyqtSignal(str)
    open_compile_log_requested = pyqtSignal(str)
    open_sim_log_requested = pyqtSignal(str)
    open_asm_file_requested = pyqtSignal(str)
    close_tab_requested = pyqtSignal(int)

    def __init__(self, parent=None):
        """初始化执行面板"""
        super().__init__(parent)
        self.case_tabs = {}  # 存储标签页
        self.max_tabs = 10  # 最大标签页数量
        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(2)
        layout.setContentsMargins(5, 5, 5, 5)

        # 创建工具按钮面板
        tool_panel = QWidget()
        tool_layout = QHBoxLayout(tool_panel)
        tool_layout.setSpacing(5)
        tool_layout.setContentsMargins(0, 0, 0, 5)

        # 工具按钮样式
        tool_btn_style = """
            QPushButton {
                background-color: #4c8bf5;
                color: white;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                min-width: 80px;
            }
            QPushButton:hover { background-color: #3d7ce4; }
            QPushButton:pressed { background-color: #3069c6; }
        """

        # 创建按钮
        verdi_btn = QPushButton("打开Verdi")
        verdi_btn.setStyleSheet(tool_btn_style)
        verdi_btn.clicked.connect(lambda: self.open_verdi_requested.emit(self.get_current_case_name()))

        verisium_btn = QPushButton("打开Verisium")
        verisium_btn.setStyleSheet(tool_btn_style)
        verisium_btn.clicked.connect(lambda: self.open_verisium_requested.emit(self.get_current_case_name()))

        compile_log_btn = QPushButton("编译日志")
        compile_log_btn.setStyleSheet(tool_btn_style)
        compile_log_btn.clicked.connect(lambda: self.open_compile_log_requested.emit(self.get_current_case_name()))

        sim_log_btn = QPushButton("仿真日志")
        sim_log_btn.setStyleSheet(tool_btn_style)
        sim_log_btn.clicked.connect(lambda: self.open_sim_log_requested.emit(self.get_current_case_name()))

        asm_btn = QPushButton("反汇编文件")
        asm_btn.setStyleSheet(tool_btn_style)
        asm_btn.clicked.connect(lambda: self.open_asm_file_requested.emit(self.get_current_case_name()))

        # 添加按钮到布局
        tool_layout.addWidget(verdi_btn)
        tool_layout.addWidget(verisium_btn)
        tool_layout.addWidget(compile_log_btn)
        tool_layout.addWidget(sim_log_btn)
        tool_layout.addWidget(asm_btn)
        tool_layout.addStretch()  # 添加弹簧,使按钮靠左对齐

        # 添加工具按钮面板到主布局
        layout.addWidget(tool_panel)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self.close_tab_requested.emit)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #cccccc;
                background: white;
                border-radius: 3px;
            }
            QTabWidget::tab-bar {
                left: 5px;
            }
            QTabBar::tab {
                background: #f0f0f0;
                border: 1px solid #cccccc;
                padding: 5px 10px;
                margin-right: 2px;
                border-top-left-radius: 3px;
                border-top-right-radius: 3px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom: 1px solid white;
            }
            QTabBar::tab:hover {
                background: #e0e0e0;
            }
        """)

        # 设置TabWidget的属性以优化性能
        self.tab_widget.setMovable(True)
        self.tab_widget.setUsesScrollButtons(True)
        self.tab_widget.setElideMode(Qt.ElideRight)
        self.tab_widget.setDocumentMode(True)

        # 连接标签页切换信号，用于管理LogPanel的可见性
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

        # 将标签页控件添加到主布局
        layout.addWidget(self.tab_widget)

        self.setLayout(layout)

    def add_log_tab(self, case_name, command):
        """添加日志标签页"""
        # 检查标签页数量
        if self.tab_widget.count() >= self.max_tabs:
            return False

        # 如果已有该用例的标签页，先关闭
        if case_name in self.case_tabs:
            tab_index = self.tab_widget.indexOf(self.case_tabs[case_name])
            self.close_tab(tab_index)

        # 创建新标签页
        log_panel = LogPanel(case_name, command)
        self.case_tabs[case_name] = log_panel

        # 添加到标签页控件，并设置为当前标签页
        tab_index = self.tab_widget.addTab(log_panel, case_name)
        self.tab_widget.setCurrentIndex(tab_index)

        # 初始化所有标签页的可见性状态
        self.on_tab_changed(tab_index)

        return True

    def close_tab(self, index):
        """关闭标签页"""
        tab = self.tab_widget.widget(index)
        if tab:
            # 确保是日志面板
            if hasattr(tab, 'case_name'):
                case_name = tab.case_name

                # 使用LogPanel的cleanup方法进行清理
                try:
                    if hasattr(tab, 'cleanup'):
                        tab.cleanup()
                    else:
                        # 兼容旧版本的清理方式
                        if hasattr(tab, 'log_timer') and tab.log_timer is not None:
                            tab.log_timer.stop()
                        if hasattr(tab, 'process') and tab.process is not None:
                            tab.stop_execution()

                except Exception as e:
                    print(f"关闭标签页时清理资源出错: {str(e)}")

                # 从字典中移除并关闭标签页
                if case_name in self.case_tabs:
                    del self.case_tabs[case_name]
                self.tab_widget.removeTab(index)

    def get_current_case_name(self):
        """获取当前标签页的用例名称"""
        current_tab = self.tab_widget.currentWidget()
        if current_tab and hasattr(current_tab, 'case_name'):
            return current_tab.case_name
        return None

    def get_log_panel(self, case_name):
        """获取指定用例的日志面板"""
        return self.case_tabs.get(case_name)

    def get_current_log_panel(self):
        """获取当前标签页的日志面板"""
        current_tab = self.tab_widget.currentWidget()
        if current_tab and hasattr(current_tab, 'case_name'):
            return current_tab
        return None

    def on_tab_changed(self, index):
        """
        处理标签页切换事件，管理LogPanel的可见性状态

        Args:
            index (int): 新的当前标签页索引
        """
        try:
            # 将所有标签页设置为不可见
            for i in range(self.tab_widget.count()):
                tab = self.tab_widget.widget(i)
                if tab and hasattr(tab, 'set_visible'):
                    tab.set_visible(False)

            # 将当前标签页设置为可见
            current_tab = self.tab_widget.widget(index)
            if current_tab and hasattr(current_tab, 'set_visible'):
                current_tab.set_visible(True)

                # 立即刷新当前标签页的日志缓冲区
                if hasattr(current_tab, 'flush_log_buffer'):
                    current_tab.flush_log_buffer()

        except Exception as e:
            print(f"处理标签页切换时出错: {str(e)}")
