"""
配置控制器
"""
import os
import random
import re
from PyQt5.QtWidgets import QFileDialog, QMessageBox
from PyQt5.QtCore import QObject, pyqtSlot, pyqtSignal

from views.config_panel import ConfigPanel
from models.command_model import CommandModel
from utils.event_bus import EventBus

class ConfigController(QObject):
    """配置控制器，负责管理配置相关操作"""

    # 定义信号
    execution_requested = pyqtSignal(str, str)  # command, case_name

    def __init__(self, main_window, config_model, history_model):
        """
        初始化配置控制器

        Args:
            main_window (MainWindow): 主窗口
            config_model (ConfigModel): 配置模型
            history_model (HistoryModel): 历史记录模型
        """
        super().__init__()
        self.main_window = main_window
        self.config_model = config_model
        self.history_model = history_model
        self.command_model = CommandModel(config_model)
        self.execution_controller = None  # 将在 set_execution_controller 方法中设置

        # 获取事件总线实例
        self.event_bus = EventBus.instance()

        # 创建配置面板
        self.config_panel = ConfigPanel()

        # 连接信号
        self.connect_signals()

    def set_execution_controller(self, execution_controller):
        """
        设置执行控制器引用

        Args:
            execution_controller (ExecutionController): 执行控制器
        """
        self.execution_controller = execution_controller

    def connect_signals(self):
        """连接信号和槽"""
        # 配置面板信号
        # 使用事件总线处理配置变更信号，避免无限递归
        self.config_panel.config_changed.connect(self.handle_config_changed)
        self.config_panel.execute_command_requested.connect(self.execute_command)
        self.config_panel.apply_history_requested.connect(self.apply_history)
        self.config_panel.re_run_history_requested.connect(self.re_run_history)
        self.config_panel.select_fsdb_file_requested.connect(self.select_fsdb_file)
        self.config_panel.clear_fsdb_file_requested.connect(self.clear_fsdb_file)
        self.config_panel.select_regr_file_requested.connect(self.select_regr_file)
        self.config_panel.clear_regr_file_requested.connect(self.clear_regr_file)
        self.config_panel.get_seed_requested.connect(self.get_seed)
        self.config_panel.parse_regr_command_requested.connect(self.parse_regr_command)

        # 命令模型信号
        self.command_model.command_generated.connect(self.update_command_preview)

    def handle_config_changed(self, config):
        """
        处理配置面板发出的配置变更信号

        Args:
            config (dict): 新的配置数据
        """
        # 更新配置模型，但不触发事件总线的配置变更信号
        self.config_model.config.update(config)

        # 使用事件总线发射配置变更信号
        self.event_bus.emit_config_changed(config)

        # 生成命令预览
        self.generate_command_preview()

    def on_config_loaded(self, config):
        """
        配置加载后的处理

        Args:
            config (dict): 配置数据
        """
        # 更新配置面板
        self.config_panel.update_config(config)

        # 生成命令预览
        self.generate_command_preview()

    @pyqtSlot(dict)
    def on_config_changed(self, config):
        """
        处理配置变更事件

        Args:
            config (dict): 新的配置数据
        """
        # 防止无限递归
        if hasattr(self, '_handling_config_change') and self._handling_config_change:
            return

        self._handling_config_change = True
        try:
            # 更新配置模型，但不触发事件总线的配置变更信号
            # 这里直接更新配置模型的内部状态，避免无限递归
            self.config_model.config.update(config)

            # 更新配置面板
            self.config_panel.blockSignals(True)  # 阻止信号传递
            self.config_panel.update_config(config)
            self.config_panel.blockSignals(False)  # 恢复信号传递

            # 生成命令预览
            self.generate_command_preview()
        finally:
            self._handling_config_change = False

    def generate_command_preview(self):
        """生成命令预览"""
        # 防止无限递归
        if hasattr(self, '_generating_preview') and self._generating_preview:
            return

        self._generating_preview = True
        try:
            # 获取当前模式
            mode = "normal"
            config = self.config_model.get_config()
            if config.get("sim_only", False):
                mode = "R"
            elif config.get("compile_only", False):
                mode = "C"

            # 获取当前用例名称
            case_name = None
            if hasattr(self.config_panel, 'case_input'):
                case_name = self.config_panel.case_input.text().strip()
                if case_name and case_name.startswith("已选择"):
                    case_name = None
                elif case_name:
                    # 更新配置模型中的用例名称
                    self.config_model.update_config({"case": case_name})

            # 获取当前配置面板的所有配置
            current_config = self.config_panel.get_current_config()

            # 更新配置模型
            self.config_model.update_config(current_config)

            # 生成命令
            command = self.command_model.generate_command(mode, case_name)

            # 确保命令预览更新到UI
            self.update_command_preview(command)
        finally:
            self._generating_preview = False

    @pyqtSlot(str)
    def update_command_preview(self, command):
        """
        更新命令预览

        Args:
            command (str): 命令字符串
        """
        self.config_panel.set_preview_text(command)

    @pyqtSlot(str, str)
    def execute_command(self, mode, case_name):
        """
        执行命令

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等
            case_name (str): 用例名称
        """
        # 检查是否是多选用例（格式为"已选择 N 个用例"）
        if case_name and case_name.startswith("已选择 "):
            # 执行多个用例
            self.execute_multiple_cases(mode)
            return

        # 验证必填字段
        config = self.config_model.get_config()

        # 检查必填字段
        if not config.get("block"):
            self.main_window.show_warning("缺少必填参数", "BLOCK参数不能为空")
            return

        # 如果没有使用回归文件，则case参数也是必填的
        if not config.get("regr_file") and not case_name and not config.get("case"):
            self.main_window.show_warning("缺少必填参数", "CASE参数不能为空")
            return

        # 验证种子号格式
        if seed := config.get("seed"):
            if not seed.isdigit():
                self.main_window.show_warning("输入格式错误", "种子号应为数字")
                return

        # 生成命令
        command = self.command_model.generate_command(mode, case_name)

        # 添加到历史记录
        self.history_model.add_command(command)

        # 更新历史记录视图
        history = self.history_model.get_history()
        self.update_history_view(history)

        # 使用事件总线发射历史记录更新信号
        self.event_bus.emit_history_updated(history)

        # 标记命令已执行
        self.command_model.mark_command_executed()

        # 使用事件总线发射命令执行信号
        self.event_bus.emit_command_executed(command, case_name)

        # 保持向后兼容
        self.execution_requested.emit(command, case_name)

    def execute_multiple_cases(self, mode):
        """
        执行多个用例

        Args:
            mode (str): 执行模式，例如 "normal", "R", "C" 等
        """
        from PyQt5.QtCore import QObject
        from PyQt5.QtWidgets import QWidget

        # 验证必填字段
        config = self.config_model.get_config()

        # 检查必填字段
        if not config.get("block"):
            self.main_window.show_warning("缺少必填参数", "BLOCK参数不能为空")
            return

        # 获取用例面板
        case_panel = None
        for child in self.main_window.findChildren(QWidget):
            if hasattr(child, 'case_tree') and isinstance(child, QWidget):
                case_panel = child
                break

        if not case_panel:
            self.main_window.show_warning("错误", "无法获取用例面板")
            return

        # 获取选中的用例
        selected_items = case_panel.case_tree.selectedItems()

        # 过滤掉文件名节点（顶层节点），只处理用例节点
        valid_items = [item for item in selected_items if item.parent() is not None]

        if not valid_items:
            self.main_window.show_warning("选择错误", "请选择要执行的用例")
            return

        # 执行每个选中的用例
        for item in valid_items:
            case_name = item.text(0)

            # 生成命令
            command = self.command_model.generate_command(mode, case_name)

            # 添加到历史记录
            self.history_model.add_command(command)

            # 使用事件总线发射命令执行信号
            self.event_bus.emit_command_executed(command, case_name)

            # 保持向后兼容
            self.execution_requested.emit(command, case_name)

        # 更新历史记录视图
        history = self.history_model.get_history()
        self.update_history_view(history)

        # 使用事件总线发射历史记录更新信号
        self.event_bus.emit_history_updated(history)

        # 显示完成消息
        self.main_window.show_message(f"已开始执行 {len(valid_items)} 个用例")

    @pyqtSlot(int)
    def apply_history(self, index):
        """
        应用历史记录

        Args:
            index (int): 历史记录索引
        """
        if index < 0:
            return

        history = self.history_model.get_history()
        if index < len(history):
            record = history[index]
            command = record.get('command', '')

            # 解析命令，更新配置
            try:
                # 分割命令参数
                parts = command.split()
                i = 0

                # 重置所有输入框和选项
                self.reset_all_inputs()

                while i < len(parts):
                    part = parts[i]
                    if part.startswith("-"):
                        # 移除开头的横杠以便匹配
                        option = part[1:]

                        # 处理基本参数
                        if option == "base" and i + 1 < len(parts):
                            self.config_panel.base_input.setText(parts[i+1])
                            i += 2
                        elif option == "block" and i + 1 < len(parts):
                            self.config_panel.block_input.setText(parts[i+1])
                            i += 2
                        elif option == "case" and i + 1 < len(parts):
                            self.config_panel.case_input.setText(parts[i+1])
                            i += 2
                        elif option == "R":
                            self.config_panel.sim_only_check.setChecked(True)
                            i += 1
                        elif option == "C":
                            self.config_panel.compile_only_check.setChecked(True)
                            i += 1
                        elif option == "cl":
                            self.config_panel.cl_check.setChecked(True)
                            i += 1
                        elif option == "fsdb":
                            self.config_panel.fsdb_check.setChecked(True)
                            if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                                # 更新配置
                                self.config_model.update_config({"fsdb_file": parts[i+1]})
                                # 更新界面
                                self.config_panel.fsdb_label.setText(os.path.basename(parts[i+1]))
                                self.config_panel.fsdb_clear_btn.setEnabled(True)
                                i += 2
                            else:
                                i += 1
                        elif option == "vwdb":
                            self.config_panel.vwdb_check.setChecked(True)
                            if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                                # 更新配置
                                self.config_model.update_config({"fsdb_file": parts[i+1]})
                                # 更新界面
                                self.config_panel.fsdb_label.setText(os.path.basename(parts[i+1]))
                                self.config_panel.fsdb_clear_btn.setEnabled(True)
                                i += 2
                            else:
                                i += 1
                        elif option == "dump_sva":
                            self.config_panel.sva_check.setChecked(True)
                            i += 1
                        elif option == "cov":
                            self.config_panel.cov_check.setChecked(True)
                            i += 1
                        elif option == "upf":
                            self.config_panel.upf_check.setChecked(True)
                            i += 1
                        elif option == "seed" and i + 1 < len(parts):
                            self.config_panel.seed_input.setText(parts[i+1])
                            i += 2
                        elif option == "dump_mem" and i + 1 < len(parts):
                            # 处理dump_mem的多选设置
                            dump_mem_value = parts[i+1]
                            if dump_mem_value:
                                # 将字符串分割为选项列表
                                options = [opt.strip() for opt in dump_mem_value.split() if opt.strip()]
                                self.config_panel.dump_mem_input.set_selected_options(options)
                            i += 2
                        elif option == "wdd" and i + 1 < len(parts):
                            self.config_panel.wdd_input.setText(parts[i+1])
                            i += 2
                        elif option == "cfg_def":
                            # 收集所有后续的非选项参数作为 cfg_def 的值
                            cfg_def_values = []
                            j = i + 1
                            while j < len(parts) and not parts[j].startswith('-'):
                                cfg_def_values.append(parts[j])
                                j += 1

                            # 如果找到了值
                            if cfg_def_values:
                                current = self.config_panel.cfg_def_input.text()
                                new_values = ' '.join(cfg_def_values)
                                if current:
                                    # 如果已有值，则追加新值
                                    self.config_panel.cfg_def_input.setText(f"{current} {new_values}")
                                else:
                                    # 如果没有现有值，直接设置
                                    self.config_panel.cfg_def_input.setText(new_values)
                            i = j  # 更新索引到下一个选项
                        elif option == "simarg" and i + 1 < len(parts):
                            # 处理带引号的参数
                            if parts[i+1].startswith('"'):
                                simarg = parts[i+1][1:]
                                j = i + 2
                                while j < len(parts) and not parts[j].endswith('"'):
                                    simarg += " " + parts[j]
                                    j += 1
                                if j < len(parts):
                                    simarg += " " + parts[j][:-1]
                                self.config_panel.simarg_input.setText(simarg)
                                i = j + 1
                            else:
                                self.config_panel.simarg_input.setText(parts[i+1])
                                i += 2
                        elif option == "post" and i + 1 < len(parts):
                            self.config_panel.post_input.setText(parts[i+1])
                            i += 2
                        elif option == "regr" and i + 1 < len(parts):
                            # 更新配置
                            self.config_model.update_config({"regr_file": parts[i+1]})
                            # 更新界面
                            self.config_panel.regr_label.setText(os.path.basename(parts[i+1]))
                            self.config_panel.clear_regr_btn.setEnabled(True)
                            i += 2
                        elif option == "fm":
                            self.config_panel.fm_check.setChecked(True)
                            i += 1
                        elif option == "bp" and i + 1 < len(parts):
                            self.config_panel.bp_input.setText(parts[i+1])
                            i += 2
                        elif option == "rundir" and i + 1 < len(parts):
                            self.config_panel.rundir_input.setText(parts[i+1])
                            i += 2
                        else:
                            # 其他选项作为额外选项
                            self.config_panel.other_options_input.setText(part + " " + " ".join(parts[i+1:]))
                            break
                    else:
                        i += 1

                # 生成命令预览
                self.generate_command_preview()

                self.main_window.show_message(f"已应用历史命令: {command[:30]}...")
            except Exception as e:
                self.main_window.show_error("解析历史命令失败", str(e))

    @pyqtSlot()
    def re_run_history(self):
        """重新执行历史命令"""
        index = self.config_panel.history_combo.currentIndex()
        if index < 0:
            return

        # 先应用历史命令到界面上
        self.apply_history(index)

        # 获取当前模式
        mode = "normal"
        config = self.config_model.get_config()
        if config.get("sim_only", False):
            mode = "R"
        elif config.get("compile_only", False):
            mode = "C"

        # 获取当前用例名称
        case_name = None
        if hasattr(self.config_panel, 'case_input'):
            case_name = self.config_panel.case_input.text().strip()
            if case_name and case_name.startswith("已选择"):
                case_name = None

        # 执行命令
        self.execute_command(mode, case_name)

        # 获取最新生成的命令
        command = self.command_model.get_current_command()
        self.main_window.show_message(f"重新执行历史命令: {command[:30]}...")

    @pyqtSlot()
    def select_fsdb_file(self):
        """选择 FSDB tcl 文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择 FSDB tcl 文件",
            "",
            "TCL 文件 (*.tcl);;所有文件 (*.*)"
        )

        if file_path:
            # 更新配置
            self.config_model.update_config({"fsdb_file": file_path})

            # 更新界面
            self.config_panel.fsdb_label.setText(os.path.basename(file_path))
            self.config_panel.fsdb_clear_btn.setEnabled(True)

            # 生成命令预览
            self.generate_command_preview()

    @pyqtSlot()
    def clear_fsdb_file(self):
        """清除 FSDB tcl 文件"""
        # 更新配置
        self.config_model.update_config({"fsdb_file": ""})

        # 更新界面
        self.config_panel.fsdb_label.setText("未选择TCL文件")
        self.config_panel.fsdb_clear_btn.setEnabled(False)

        # 生成命令预览
        self.generate_command_preview()

    @pyqtSlot()
    def select_regr_file(self):
        """选择回归列表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择回归列表文件",
            "",
            "回归列表文件 (*.list *.txt);;所有文件 (*.*)"
        )

        if file_path:
            # 更新配置
            self.config_model.update_config({"regr_file": file_path})

            # 更新界面
            self.config_panel.regr_label.setText(os.path.basename(file_path))
            self.config_panel.clear_regr_btn.setEnabled(True)

            # 生成命令预览
            self.generate_command_preview()

    @pyqtSlot()
    def clear_regr_file(self):
        """清除回归列表文件"""
        # 更新配置
        self.config_model.update_config({"regr_file": ""})

        # 更新界面
        self.config_panel.regr_label.setText("未选择回归文件")
        self.config_panel.clear_regr_btn.setEnabled(False)

        # 生成命令预览
        self.generate_command_preview()

    @pyqtSlot()
    def get_seed(self):
        """从仿真日志文件中获取种子号，并更新到种子号输入框"""
        # 检查执行控制器是否已设置
        if not self.execution_controller:
            # 尝试查找执行控制器
            for controller in self.main_window.findChildren(QObject):
                if hasattr(controller, 'execution_panel') and hasattr(controller.execution_panel, 'tab_widget'):
                    self.execution_controller = controller
                    break

            if not self.execution_controller:
                QMessageBox.warning(self.main_window, "错误", "无法获取执行控制器")
                return

        # 获取当前标签页
        current_tab = self.execution_controller.execution_panel.get_current_log_panel()
        if not current_tab or not hasattr(current_tab, 'case_name'):
            QMessageBox.warning(self.main_window, "错误", "请先选择一个用例标签页")
            return

        case_name = current_tab.case_name
        log_file = f"{case_name}/log/irun_sim.log"  # 构建仿真日志文件路径

        if not os.path.exists(log_file):
            # 尝试其他可能的日志文件名
            alternative_logs = [
                f"{case_name}/log/vcs_sim.log",
                f"{case_name}/log/sim.log",
                f"{case_name}/irun_sim.log",
                f"{case_name}/vcs_sim.log",
                f"{case_name}/sim.log"
            ]

            for alt_log in alternative_logs:
                if os.path.exists(alt_log):
                    log_file = alt_log
                    break
            else:
                QMessageBox.warning(self.main_window, "错误", f"找不到仿真日志文件：{log_file}")
                return

        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                # 使用正则表达式查找种子号，匹配 -seed 后面的数字
                if match := re.search(r'-seed\s+(\d+)', content):
                    seed = match.group(1)
                    # 更新配置
                    self.config_model.update_config({"seed": seed})
                    # 更新界面
                    self.config_panel.seed_input.setText(seed)
                    # 生成命令预览
                    self.generate_command_preview()
                    self.main_window.show_message(f"已获取种子号：{seed}", 3000)
                else:
                    QMessageBox.warning(self.main_window, "错误", "在仿真日志中未找到种子号")
        except Exception as e:
            QMessageBox.critical(self.main_window, "错误", f"读取日志文件失败：{str(e)}")

    @pyqtSlot()
    def parse_regr_command(self):
        """解析回归指令"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QTextEdit, QPushButton

        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("解析回归指令")
        dialog.setMinimumWidth(600)

        layout = QVBoxLayout()

        # 添加说明标签
        hint_label = QLabel("请输入回归用例指令:")
        hint_label.setStyleSheet("color: #666;")
        layout.addWidget(hint_label)

        # 添加文本输入框
        text_edit = QTextEdit()
        text_edit.setPlaceholderText("示例: runsim -base top -block udtb/top/lowpower -case top_frc_pd_wkup_seq ...")
        text_edit.setMinimumHeight(100)
        layout.addWidget(text_edit)

        # 按钮区域
        btn_layout = QHBoxLayout()
        parse_btn = QPushButton("解析")
        cancel_btn = QPushButton("取消")

        btn_layout.addWidget(parse_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接信号
        parse_btn.clicked.connect(lambda: self.do_parse_command(text_edit.toPlainText(), dialog))
        cancel_btn.clicked.connect(dialog.reject)

        dialog.exec_()

    def do_parse_command(self, command_text, dialog):
        """解析回归指令并填充到界面"""
        from PyQt5.QtWidgets import QMessageBox

        if not command_text.strip():
            QMessageBox.warning(dialog, "错误", "请输入回归指令")
            return

        try:
            # 分割命令参数
            parts = command_text.split()
            i = 0

            # 重置所有输入框和选项
            self.reset_all_inputs()

            while i < len(parts):
                part = parts[i]
                if part.startswith("-"):
                    # 移除开头的横杠以便匹配
                    option = part[1:]

                    # 处理基本参数
                    if option == "base" and i + 1 < len(parts):
                        self.config_panel.base_input.setText(parts[i+1])
                        i += 2
                    elif option == "block" and i + 1 < len(parts):
                        self.config_panel.block_input.setText(parts[i+1])
                        i += 2
                    elif option == "case" and i + 1 < len(parts):
                        self.config_panel.case_input.setText(parts[i+1])
                        i += 2
                    elif option == "R":
                        self.config_panel.sim_only_check.setChecked(True)
                        i += 1
                    elif option == "C":
                        self.config_panel.compile_only_check.setChecked(True)
                        i += 1
                    elif option == "cl":
                        self.config_panel.cl_check.setChecked(True)
                        i += 1
                    elif option == "fsdb":
                        self.config_panel.fsdb_check.setChecked(True)
                        if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                            # 更新配置
                            self.config_model.update_config({"fsdb_file": parts[i+1]})
                            # 更新界面
                            self.config_panel.fsdb_label.setText(os.path.basename(parts[i+1]))
                            self.config_panel.fsdb_clear_btn.setEnabled(True)
                            i += 2
                        else:
                            i += 1
                    elif option == "vwdb":
                        self.config_panel.vwdb_check.setChecked(True)
                        if i + 1 < len(parts) and not parts[i+1].startswith("-"):
                            # 更新配置
                            self.config_model.update_config({"fsdb_file": parts[i+1]})
                            # 更新界面
                            self.config_panel.fsdb_label.setText(os.path.basename(parts[i+1]))
                            self.config_panel.fsdb_clear_btn.setEnabled(True)
                            i += 2
                        else:
                            i += 1
                    elif option == "dump_sva":
                        self.config_panel.sva_check.setChecked(True)
                        i += 1
                    elif option == "cov":
                        self.config_panel.cov_check.setChecked(True)
                        i += 1
                    elif option == "upf":
                        self.config_panel.upf_check.setChecked(True)
                        i += 1
                    elif option == "seed" and i + 1 < len(parts):
                        self.config_panel.seed_input.setText(parts[i+1])
                        i += 2
                    elif option == "dump_mem" and i + 1 < len(parts):
                        # 处理dump_mem的多选设置
                        dump_mem_value = parts[i+1]
                        if dump_mem_value:
                            # 将字符串分割为选项列表
                            options = [opt.strip() for opt in dump_mem_value.split() if opt.strip()]
                            self.config_panel.dump_mem_input.set_selected_options(options)
                        i += 2
                    elif option == "wdd" and i + 1 < len(parts):
                        self.config_panel.wdd_input.setText(parts[i+1])
                        i += 2
                    elif option == "cfg_def":
                        # 收集所有后续的非选项参数作为 cfg_def 的值
                        cfg_def_values = []
                        j = i + 1
                        while j < len(parts) and not parts[j].startswith('-'):
                            cfg_def_values.append(parts[j])
                            j += 1

                        # 如果找到了值
                        if cfg_def_values:
                            current = self.config_panel.cfg_def_input.text()
                            new_values = ' '.join(cfg_def_values)
                            if current:
                                # 如果已有值，则追加新值
                                self.config_panel.cfg_def_input.setText(f"{current} {new_values}")
                            else:
                                # 如果没有现有值，直接设置
                                self.config_panel.cfg_def_input.setText(new_values)
                        i = j  # 更新索引到下一个选项
                    elif option == "simarg" and i + 1 < len(parts):
                        # 处理带引号的参数
                        if parts[i+1].startswith('"'):
                            simarg = parts[i+1][1:]
                            j = i + 2
                            while j < len(parts) and not parts[j].endswith('"'):
                                simarg += " " + parts[j]
                                j += 1
                            if j < len(parts):
                                simarg += " " + parts[j][:-1]
                            self.config_panel.simarg_input.setText(simarg)
                            i = j + 1
                        else:
                            self.config_panel.simarg_input.setText(parts[i+1])
                            i += 2
                    elif option == "post" and i + 1 < len(parts):
                        self.config_panel.post_input.setText(parts[i+1])
                        i += 2
                    elif option == "regr" and i + 1 < len(parts):
                        # 更新配置
                        self.config_model.update_config({"regr_file": parts[i+1]})
                        # 更新界面
                        self.config_panel.regr_label.setText(os.path.basename(parts[i+1]))
                        self.config_panel.clear_regr_btn.setEnabled(True)
                        i += 2
                    elif option == "fm":
                        self.config_panel.fm_check.setChecked(True)
                        i += 1
                    elif option == "bp" and i + 1 < len(parts):
                        self.config_panel.bp_input.setText(parts[i+1])
                        i += 2
                    elif option == "rundir" and i + 1 < len(parts):
                        self.config_panel.rundir_input.setText(parts[i+1])
                        i += 2
                    else:
                        # 其他选项作为额外选项
                        self.config_panel.other_options_input.setText(part + " " + " ".join(parts[i+1:]))
                        break
                else:
                    i += 1

            dialog.accept()
            self.main_window.show_message("回归指令解析完成", 3000)

            # 生成命令预览
            self.generate_command_preview()

        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"解析指令失败: {str(e)}")

    def reset_all_inputs(self):
        """重置所有输入框和选项，确保不影响多值参数的处理"""
        # 清空输入框，但保留值分隔符
        self.config_panel.base_input.clear()
        self.config_panel.block_input.clear()
        self.config_panel.case_input.clear()
        self.config_panel.rundir_input.clear()
        self.config_panel.other_options_input.clear()
        self.config_panel.dump_mem_input.clear()
        self.config_panel.wdd_input.clear()
        self.config_panel.seed_input.clear()
        self.config_panel.simarg_input.clear()
        self.config_panel.cfg_def_input.clear()  # 清空时不会影响多个值的处理逻辑
        self.config_panel.post_input.clear()
        self.config_panel.bp_input.clear()

        # 取消所有复选框
        self.config_panel.fsdb_check.setChecked(False)
        self.config_panel.vwdb_check.setChecked(False)
        self.config_panel.cl_check.setChecked(False)
        self.config_panel.sva_check.setChecked(False)
        self.config_panel.sim_only_check.setChecked(False)
        self.config_panel.compile_only_check.setChecked(False)
        self.config_panel.cov_check.setChecked(False)
        self.config_panel.upf_check.setChecked(False)
        self.config_panel.fm_check.setChecked(False)

        # 清除文件选择
        self.clear_fsdb_file()
        self.clear_regr_file()

    def update_history_view(self, history):
        """
        更新历史记录视图

        Args:
            history (list): 历史记录列表
        """
        self.config_panel.update_history_combo(history)
