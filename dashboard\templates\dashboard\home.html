{% extends 'dashboard/base.html' %}

{% block title %}仪表盘首页 - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 统计卡片 -->
    <div class="col-md-3 mb-4">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_projects }}</h4>
                        <p class="card-text">活跃项目</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-project-diagram fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_testcases }}</h4>
                        <p class="card-text">总测试用例</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ passed_testcases }}</h4>
                        <p class="card-text">通过用例</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-4">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ open_bugs }}</h4>
                        <p class="card-text">待处理BUG</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-bug fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最近项目 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-project-diagram me-2"></i>最近项目
                </h5>
            </div>
            <div class="card-body">
                {% if recent_projects %}
                    <div class="list-group list-group-flush">
                        {% for project in recent_projects %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">
                                        <a href="{% url 'dashboard:project_detail' project.id %}" class="text-decoration-none">
                                            {{ project.name }}
                                        </a>
                                    </h6>
                                    <small class="text-muted">{{ project.description|truncatechars:50 }}</small>
                                </div>
                                <small class="text-muted">{{ project.updated_at|date:"m-d H:i" }}</small>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">暂无项目</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 最近执行记录 -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>最近执行记录
                </h5>
            </div>
            <div class="card-body">
                {% if recent_executions %}
                    <div class="list-group list-group-flush">
                        {% for execution in recent_executions %}
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ execution.testcase.testcase_name|truncatechars:30 }}</h6>
                                    <small class="text-muted">
                                        {% if execution.result == 'pass' %}
                                            <span class="badge bg-success">通过</span>
                                        {% elif execution.result == 'fail' %}
                                            <span class="badge bg-danger">失败</span>
                                        {% elif execution.result == 'running' %}
                                            <span class="badge bg-primary">运行中</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ execution.get_result_display }}</span>
                                        {% endif %}
                                    </small>
                                </div>
                                <small class="text-muted">{{ execution.start_time|date:"m-d H:i" }}</small>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">暂无执行记录</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>用例状态分布
                </h5>
            </div>
            <div class="card-body">
                <canvas id="testcaseStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>BUG趋势
                </h5>
            </div>
            <div class="card-body">
                <canvas id="bugTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 用例状态分布图
const testcaseCtx = document.getElementById('testcaseStatusChart').getContext('2d');
const testcaseChart = new Chart(testcaseCtx, {
    type: 'doughnut',
    data: {
        labels: ['通过', '失败', '进行中', '未开始'],
        datasets: [{
            data: [{{ passed_testcases }}, 10, 5, {{ total_testcases|add:"-15" }}],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#ffc107',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// BUG趋势图
const bugCtx = document.getElementById('bugTrendChart').getContext('2d');
const bugChart = new Chart(bugCtx, {
    type: 'line',
    data: {
        labels: ['本周一', '本周二', '本周三', '本周四', '本周五', '本周六', '本周日'],
        datasets: [{
            label: '新增BUG',
            data: [2, 1, 3, 0, 2, 1, 0],
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.4
        }, {
            label: '解决BUG',
            data: [1, 2, 1, 3, 1, 2, 1],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>
{% endblock %}
