<div class="row">
    <!-- 基本信息 -->
    <div class="col-md-6">
        <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-1"></i>基本信息</h6>
        <table class="table table-sm">
            <tr>
                <td width="30%" class="fw-bold">BUG ID:</td>
                <td><span class="badge bg-primary">{{ bug.bug_id }}</span></td>
            </tr>
            <tr>
                <td class="fw-bold">标题:</td>
                <td>{{ bug.title }}</td>
            </tr>
            <tr>
                <td class="fw-bold">所属项目:</td>
                <td>{{ bug.project.name }}</td>
            </tr>
            <tr>
                <td class="fw-bold">BUG类型:</td>
                <td><span class="badge bg-secondary">{{ bug.get_bug_type_display }}</span></td>
            </tr>
            <tr>
                <td class="fw-bold">严重程度:</td>
                <td>
                    {% if bug.severity == 'critical' %}
                        <span class="badge bg-danger">{{ bug.get_severity_display }}</span>
                    {% elif bug.severity == 'major' %}
                        <span class="badge bg-warning">{{ bug.get_severity_display }}</span>
                    {% elif bug.severity == 'minor' %}
                        <span class="badge bg-info">{{ bug.get_severity_display }}</span>
                    {% else %}
                        <span class="badge bg-light text-dark">{{ bug.get_severity_display }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="fw-bold">验证阶段:</td>
                <td><span class="badge bg-info">{{ bug.get_verification_phase_display }}</span></td>
            </tr>
        </table>
    </div>
    
    <!-- 状态和人员信息 -->
    <div class="col-md-6">
        <h6 class="text-primary mb-3"><i class="fas fa-users me-1"></i>状态和人员</h6>
        <table class="table table-sm">
            <tr>
                <td width="30%" class="fw-bold">当前状态:</td>
                <td>
                    {% if bug.status == 'open' %}
                        <span class="badge bg-danger">{{ bug.get_status_display }}</span>
                    {% elif bug.status == 'in_progress' %}
                        <span class="badge bg-warning">{{ bug.get_status_display }}</span>
                    {% elif bug.status == 'resolved' %}
                        <span class="badge bg-success">{{ bug.get_status_display }}</span>
                    {% elif bug.status == 'closed' %}
                        <span class="badge bg-secondary">{{ bug.get_status_display }}</span>
                    {% else %}
                        <span class="badge bg-dark">{{ bug.get_status_display }}</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="fw-bold">提交者:</td>
                <td>{{ bug.submitter.username }}</td>
            </tr>
            <tr>
                <td class="fw-bold">验证人:</td>
                <td>
                    {% if bug.assignee %}
                        {{ bug.assignee.username }}
                    {% else %}
                        <span class="text-muted">未分配</span>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="fw-bold">创建时间:</td>
                <td>{{ bug.created_at|date:"Y-m-d H:i:s" }}</td>
            </tr>
            <tr>
                <td class="fw-bold">更新时间:</td>
                <td>{{ bug.updated_at|date:"Y-m-d H:i:s" }}</td>
            </tr>
            <tr>
                <td class="fw-bold">解决时间:</td>
                <td>
                    {% if bug.resolved_at %}
                        {{ bug.resolved_at|date:"Y-m-d H:i:s" }}
                    {% else %}
                        <span class="text-muted">未解决</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- 问题描述 -->
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary mb-3"><i class="fas fa-file-alt me-1"></i>问题描述</h6>
        <div class="card">
            <div class="card-body">
                <p class="mb-0">{{ bug.description|linebreaks }}</p>
            </div>
        </div>
    </div>
</div>

<!-- 发现信息 -->
<div class="row mt-3">
    <div class="col-md-6">
        <h6 class="text-primary mb-3"><i class="fas fa-search me-1"></i>发现信息</h6>
        <table class="table table-sm">
            <tr>
                <td width="40%" class="fw-bold">发现平台:</td>
                <td>{{ bug.discovered_platform|default:"未填写" }}</td>
            </tr>
            <tr>
                <td class="fw-bold">发现用例:</td>
                <td>
                    {% if bug.discovered_testcase %}
                        <code>{{ bug.discovered_testcase }}</code>
                    {% else %}
                        <span class="text-muted">未填写</span>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
    
    <!-- 操作按钮 -->
    <div class="col-md-6">
        <h6 class="text-primary mb-3"><i class="fas fa-cogs me-1"></i>操作</h6>
        <div class="d-grid gap-2">
            {% if bug.status == 'open' %}
                <button class="btn btn-warning btn-sm" onclick="updateBugStatusInModal({{ bug.id }}, 'in_progress')">
                    <i class="fas fa-play me-1"></i>开始处理
                </button>
            {% elif bug.status == 'in_progress' %}
                <button class="btn btn-success btn-sm" onclick="updateBugStatusInModal({{ bug.id }}, 'resolved')">
                    <i class="fas fa-check me-1"></i>标记解决
                </button>
            {% elif bug.status == 'resolved' %}
                <button class="btn btn-secondary btn-sm" onclick="updateBugStatusInModal({{ bug.id }}, 'closed')">
                    <i class="fas fa-archive me-1"></i>关闭BUG
                </button>
            {% endif %}
            
            <a href="{% url 'dashboard:edit_bug' bug.id %}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-edit me-1"></i>编辑BUG
            </a>
        </div>
    </div>
</div>

<!-- 时间线 -->
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary mb-3"><i class="fas fa-history me-1"></i>状态历史</h6>
        <div class="timeline">
            <div class="timeline-item">
                <div class="timeline-marker bg-primary"></div>
                <div class="timeline-content">
                    <h6 class="timeline-title">BUG创建</h6>
                    <p class="timeline-text">由 {{ bug.submitter.username }} 创建</p>
                    <small class="text-muted">{{ bug.created_at|date:"Y-m-d H:i:s" }}</small>
                </div>
            </div>
            
            {% if bug.status != 'open' %}
                <div class="timeline-item">
                    <div class="timeline-marker bg-warning"></div>
                    <div class="timeline-content">
                        <h6 class="timeline-title">状态更新</h6>
                        <p class="timeline-text">状态变更为: {{ bug.get_status_display }}</p>
                        <small class="text-muted">{{ bug.updated_at|date:"Y-m-d H:i:s" }}</small>
                    </div>
                </div>
            {% endif %}
            
            {% if bug.resolved_at %}
                <div class="timeline-item">
                    <div class="timeline-marker bg-success"></div>
                    <div class="timeline-content">
                        <h6 class="timeline-title">BUG解决</h6>
                        <p class="timeline-text">BUG已解决</p>
                        <small class="text-muted">{{ bug.resolved_at|date:"Y-m-d H:i:s" }}</small>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: 600;
}

.timeline-text {
    margin-bottom: 5px;
    font-size: 13px;
}
</style>

<script>
function updateBugStatusInModal(bugId, status) {
    if (confirm('确定要更新BUG状态吗？')) {
        $.ajax({
            url: '/api/update_bug_status/',
            type: 'POST',
            data: {
                'bug_id': bugId,
                'status': status,
                'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    // 关闭模态框并刷新页面
                    $('#bugDetailModal').modal('hide');
                    location.reload();
                } else {
                    alert('状态更新失败: ' + response.message);
                }
            },
            error: function() {
                alert('网络错误，请稍后重试');
            }
        });
    }
}
</script>
