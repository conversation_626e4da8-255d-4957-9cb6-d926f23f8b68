"""
RunSim GUI集成接口模块
"""
import os
import re
import json
import time
import threading
from datetime import datetime
from django.utils import timezone
from django.conf import settings
from .models import TestCase, ExecutionRecord
from .views import parse_command_params, determine_status_field


class RunSimIntegration:
    """RunSim GUI集成类"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.log_watchers = {}
    
    def start_monitoring(self):
        """开始监控RunSim执行"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()
            print("RunSim监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        print("RunSim监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 这里可以监控RunSim的执行状态
                # 实际实现时需要根据RunSim的具体接口来调整
                time.sleep(5)  # 每5秒检查一次
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(10)
    
    def on_simulation_start(self, command, case_name, run_dir):
        """仿真开始时的回调"""
        try:
            # 解析命令参数
            params = parse_command_params(command)
            
            # 查找对应的测试用例
            testcase = TestCase.objects.filter(testcase_name=case_name).first()
            if not testcase:
                print(f"警告: 未找到测试用例 {case_name}")
                return
            
            # 确定要更新的状态字段
            status_field = determine_status_field(params)
            
            # 更新用例状态为进行中
            setattr(testcase, status_field, 'on_going')
            testcase.start_time = timezone.now()
            testcase.save()
            
            # 创建执行记录
            execution_record = ExecutionRecord.objects.create(
                testcase=testcase,
                command=command,
                case_param=case_name,
                base_param=params.get('base', ''),
                block_param=params.get('block', ''),
                post_param=params.get('post', ''),
                result='running',
                start_time=testcase.start_time,
                log_file_path=os.path.join(run_dir, 'irun_sim.log') if run_dir else '',
                executor_id=1  # 默认用户，实际应该从RunSim获取
            )
            
            # 开始监控日志文件
            if run_dir:
                log_file = os.path.join(run_dir, 'irun_sim.log')
                self._start_log_monitoring(execution_record.id, log_file)
            
            print(f"仿真开始: {case_name} - 状态已更新为进行中")
            
        except Exception as e:
            print(f"处理仿真开始事件时出错: {e}")
    
    def on_simulation_complete(self, case_name, run_dir, success=None):
        """仿真完成时的回调"""
        try:
            # 查找对应的测试用例
            testcase = TestCase.objects.filter(testcase_name=case_name).first()
            if not testcase:
                print(f"警告: 未找到测试用例 {case_name}")
                return
            
            # 查找最近的执行记录
            execution_record = ExecutionRecord.objects.filter(
                testcase=testcase,
                result='running'
            ).order_by('-start_time').first()
            
            if not execution_record:
                print(f"警告: 未找到执行记录 {case_name}")
                return
            
            # 检查仿真结果
            if success is None:
                success = self._check_simulation_result(run_dir)
            
            # 更新执行记录
            execution_record.end_time = timezone.now()
            execution_record.duration = execution_record.end_time - execution_record.start_time
            execution_record.result = 'pass' if success else 'fail'
            execution_record.save()
            
            # 更新测试用例状态
            command = execution_record.command
            params = parse_command_params(command)
            status_field = determine_status_field(params)
            
            if success:
                setattr(testcase, status_field, 'pass')
                testcase.end_time = execution_record.end_time
                testcase.actual_time = execution_record.duration
            else:
                setattr(testcase, status_field, 'fail')
                # 失败时不更新结束时间
            
            testcase.save()
            
            # 停止日志监控
            self._stop_log_monitoring(execution_record.id)
            
            result_text = "通过" if success else "失败"
            print(f"仿真完成: {case_name} - 结果: {result_text}")
            
        except Exception as e:
            print(f"处理仿真完成事件时出错: {e}")
    
    def _check_simulation_result(self, run_dir):
        """检查仿真结果"""
        if not run_dir:
            return False
        
        log_file = os.path.join(run_dir, 'irun_sim.log')
        if not os.path.exists(log_file):
            return False
        
        try:
            # 读取日志文件的最后50行
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
                last_lines = lines[-50:] if len(lines) > 50 else lines
                
                # 检查是否包含"SPRD_PASSED"
                for line in last_lines:
                    if 'SPRD_PASSED' in line:
                        return True
                
                return False
                
        except Exception as e:
            print(f"读取日志文件时出错: {e}")
            return False
    
    def _start_log_monitoring(self, execution_id, log_file):
        """开始监控日志文件"""
        if execution_id not in self.log_watchers:
            watcher = LogFileWatcher(execution_id, log_file, self._on_log_update)
            watcher.start()
            self.log_watchers[execution_id] = watcher
    
    def _stop_log_monitoring(self, execution_id):
        """停止监控日志文件"""
        if execution_id in self.log_watchers:
            watcher = self.log_watchers[execution_id]
            watcher.stop()
            del self.log_watchers[execution_id]
    
    def _on_log_update(self, execution_id, new_lines):
        """日志更新回调"""
        # 这里可以实时处理日志更新
        # 例如检查错误信息、进度信息等
        pass
    
    def get_status(self):
        """获取集成状态"""
        return {
            'monitoring': self.monitoring,
            'active_watchers': len(self.log_watchers),
            'last_check': datetime.now().isoformat()
        }


class LogFileWatcher:
    """日志文件监控器"""
    
    def __init__(self, execution_id, log_file, callback):
        self.execution_id = execution_id
        self.log_file = log_file
        self.callback = callback
        self.watching = False
        self.thread = None
        self.last_position = 0
    
    def start(self):
        """开始监控"""
        if not self.watching:
            self.watching = True
            self.thread = threading.Thread(target=self._watch_loop)
            self.thread.daemon = True
            self.thread.start()
    
    def stop(self):
        """停止监控"""
        self.watching = False
        if self.thread:
            self.thread.join()
    
    def _watch_loop(self):
        """监控循环"""
        while self.watching:
            try:
                if os.path.exists(self.log_file):
                    with open(self.log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        f.seek(self.last_position)
                        new_lines = f.readlines()
                        if new_lines:
                            self.callback(self.execution_id, new_lines)
                            self.last_position = f.tell()
                
                time.sleep(1)  # 每秒检查一次
                
            except Exception as e:
                print(f"监控日志文件时出错: {e}")
                time.sleep(5)


# 全局集成实例
runsim_integration = RunSimIntegration()


def integrate_with_runsim_gui():
    """与RunSim GUI集成的主函数"""
    """
    这个函数应该在RunSim GUI启动时调用，用于建立与仪表盘系统的连接
    
    集成方式可以是：
    1. 通过共享内存
    2. 通过文件系统监控
    3. 通过网络接口
    4. 通过数据库
    
    具体实现需要根据RunSim GUI的架构来调整
    """
    
    # 启动监控
    runsim_integration.start_monitoring()
    
    # 这里可以添加与RunSim GUI的具体集成代码
    # 例如：
    # - 注册回调函数
    # - 建立通信通道
    # - 设置事件监听器
    
    print("RunSim GUI集成已启动")
    
    return runsim_integration


def create_runsim_hooks():
    """创建RunSim钩子函数"""
    """
    这些函数可以被RunSim GUI调用，用于通知仪表盘系统执行状态的变化
    """
    
    def on_execution_start(command, case_name, run_dir=None):
        """执行开始钩子"""
        runsim_integration.on_simulation_start(command, case_name, run_dir)
    
    def on_execution_complete(case_name, run_dir=None, success=None):
        """执行完成钩子"""
        runsim_integration.on_simulation_complete(case_name, run_dir, success)
    
    return {
        'on_execution_start': on_execution_start,
        'on_execution_complete': on_execution_complete,
        'get_status': runsim_integration.get_status,
    }
