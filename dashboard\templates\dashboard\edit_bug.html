{% extends 'dashboard/base.html' %}

{% block title %}编辑BUG - {{ bug.bug_id }} - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>编辑BUG - {{ bug.bug_id }}
                    </h4>
                </div>
                <div class="card-body">
                    <form id="editBugForm">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="bug_id" class="form-label">BUG ID</label>
                                    <input type="text" class="form-control" id="bug_id" name="bug_id" 
                                           value="{{ bug.bug_id }}" readonly>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="project_id" class="form-label">所属项目</label>
                                    <select class="form-select" id="project_id" name="project_id" disabled>
                                        {% for project in projects %}
                                            <option value="{{ project.id }}" {% if project.id == bug.project.id %}selected{% endif %}>
                                                {{ project.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">BUG标题 *</label>
                            <input type="text" class="form-control" id="title" name="title" 
                                   value="{{ bug.title }}" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">问题描述 *</label>
                            <textarea class="form-control" id="description" name="description" rows="4" 
                                      required>{{ bug.description }}</textarea>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="bug_type" class="form-label">BUG类型 *</label>
                                    <select class="form-select" id="bug_type" name="bug_type" required>
                                        <option value="functional" {% if bug.bug_type == 'functional' %}selected{% endif %}>功能性</option>
                                        <option value="performance" {% if bug.bug_type == 'performance' %}selected{% endif %}>性能</option>
                                        <option value="interface" {% if bug.bug_type == 'interface' %}selected{% endif %}>接口</option>
                                        <option value="timing" {% if bug.bug_type == 'timing' %}selected{% endif %}>时序</option>
                                        <option value="power" {% if bug.bug_type == 'power' %}selected{% endif %}>功耗</option>
                                        <option value="other" {% if bug.bug_type == 'other' %}selected{% endif %}>其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="severity" class="form-label">严重程度 *</label>
                                    <select class="form-select" id="severity" name="severity" required>
                                        <option value="critical" {% if bug.severity == 'critical' %}selected{% endif %}>严重</option>
                                        <option value="major" {% if bug.severity == 'major' %}selected{% endif %}>主要</option>
                                        <option value="minor" {% if bug.severity == 'minor' %}selected{% endif %}>次要</option>
                                        <option value="trivial" {% if bug.severity == 'trivial' %}selected{% endif %}>轻微</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="verification_phase" class="form-label">验证阶段 *</label>
                                    <select class="form-select" id="verification_phase" name="verification_phase" required>
                                        <option value="dvr1" {% if bug.verification_phase == 'dvr1' %}selected{% endif %}>DVR1</option>
                                        <option value="dvr2" {% if bug.verification_phase == 'dvr2' %}selected{% endif %}>DVR2</option>
                                        <option value="dvr3" {% if bug.verification_phase == 'dvr3' %}selected{% endif %}>DVR3</option>
                                        <option value="dvs1" {% if bug.verification_phase == 'dvs1' %}selected{% endif %}>DVS1</option>
                                        <option value="dvs2" {% if bug.verification_phase == 'dvs2' %}selected{% endif %}>DVS2</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="status" class="form-label">状态</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="open" {% if bug.status == 'open' %}selected{% endif %}>打开</option>
                                        <option value="in_progress" {% if bug.status == 'in_progress' %}selected{% endif %}>处理中</option>
                                        <option value="resolved" {% if bug.status == 'resolved' %}selected{% endif %}>已解决</option>
                                        <option value="closed" {% if bug.status == 'closed' %}selected{% endif %}>已关闭</option>
                                        <option value="rejected" {% if bug.status == 'rejected' %}selected{% endif %}>已拒绝</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discovered_platform" class="form-label">发现平台</label>
                                    <input type="text" class="form-control" id="discovered_platform" name="discovered_platform" 
                                           value="{{ bug.discovered_platform }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="discovered_testcase" class="form-label">发现用例</label>
                                    <input type="text" class="form-control" id="discovered_testcase" name="discovered_testcase" 
                                           value="{{ bug.discovered_testcase }}">
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'dashboard:bug_list' %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-arrow-left me-1"></i>返回
                            </a>
                            <button type="submit" class="btn btn-primary" id="saveBtn">
                                <i class="fas fa-save me-1"></i>保存更改
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#editBugForm').submit(function(e) {
        e.preventDefault();
        
        // 验证表单
        if (!this.checkValidity()) {
            this.reportValidity();
            return;
        }
        
        // 禁用保存按钮
        $('#saveBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-1"></span>保存中...');
        
        // 发送AJAX请求
        $.ajax({
            url: '{% url "dashboard:edit_bug" bug.id %}',
            type: 'POST',
            data: $(this).serialize(),
            success: function(response) {
                if (response.success) {
                    // 成功提示
                    showAlert('success', response.message);
                    
                    // 3秒后跳转到BUG列表
                    setTimeout(function() {
                        window.location.href = '{% url "dashboard:bug_list" %}';
                    }, 3000);
                } else {
                    // 错误提示
                    showAlert('danger', response.message);
                    $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存更改');
                }
            },
            error: function(xhr, status, error) {
                $('#saveBtn').prop('disabled', false).html('<i class="fas fa-save me-1"></i>保存更改');
                
                var message = '保存失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                
                showAlert('danger', message);
            }
        });
    });
    
    function showAlert(type, message) {
        var alertHtml = '<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' +
                       message +
                       '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' +
                       '</div>';
        
        // 移除现有的alert
        $('.alert').remove();
        
        // 添加新的alert
        $('.container-fluid').prepend(alertHtml);
        
        // 滚动到顶部
        $('html, body').animate({scrollTop: 0}, 500);
        
        // 5秒后自动隐藏
        setTimeout(function() {
            $('.alert').fadeOut();
        }, 5000);
    }
});
</script>
{% endblock %}
