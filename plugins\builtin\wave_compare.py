from PyQt5.QtWidgets import (QAction, QDialog, QVBoxLayout, QHBoxLayout, QPushButton,
                           QFileDialog, QLabel, QTextEdit, QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt
import os
import subprocess
import difflib
import re
from plugins.base import PluginBase

class WaveComparePlugin(PluginBase):
    """波形对比插件"""

    @property
    def name(self):
        return "波形对比器"

    @property
    def version(self):
        return "1.0.0"

    @property
    def description(self):
        return "比较两个波形文件的差异"

    def __init__(self):
        super().__init__()
        # 对话框引用
        self.current_dialog = None

    def initialize(self, main_window):
        """初始化插件"""
        try:
            self.main_window = main_window

            # 创建菜单项
            self.menu_action = QAction(self.name, main_window)
            self.menu_action.setStatusTip(self.description)
            self.menu_action.triggered.connect(self.show_compare_dialog)

            # 添加到工具菜单
            if hasattr(main_window, 'tools_menu'):
                self.main_window.tools_menu.addSeparator()
                self.main_window.tools_menu.addAction(self.menu_action)

            # 创建状态指示器（初始不可见）
            self.create_status_indicator()

        except Exception as e:
            print(f"初始化波形对比插件失败: {str(e)}")

    def create_status_indicator(self):
        """创建状态指示器"""
        try:
            from PyQt5.QtWidgets import QLabel, QToolButton

            # 创建状态指示器标签
            self.status_label = QLabel("波形对比器运行中")
            self.status_label.setVisible(False)  # 初始不可见

            # 创建一个工具按钮，可以点击
            self.status_button = QToolButton()
            self.status_button.setText("查看")
            self.status_button.setVisible(False)  # 初始不可见

            # 添加点击事件，显示对话框
            self.status_button.clicked.connect(self.show_running_dialog)

            # 添加到状态栏
            if hasattr(self.main_window, 'status_bar'):
                self.main_window.status_bar.addPermanentWidget(QLabel(""))  # 添加间隔
                self.main_window.status_bar.addPermanentWidget(self.status_label)
                self.main_window.status_bar.addPermanentWidget(self.status_button)
        except Exception as e:
            print(f"创建状态指示器失败: {str(e)}")

    def show_running_dialog(self):
        """显示正在运行的对话框"""
        if self.current_dialog:
            # 如果对话框存在但被隐藏，则显示它
            if not self.current_dialog.isVisible():
                self.current_dialog.showNormal()
            # 如果对话框被最小化，则恢复它
            elif self.current_dialog.isMinimized():
                self.current_dialog.showNormal()
            # 将对话框置于前台
            self.current_dialog.raise_()
            self.current_dialog.activateWindow()

    def cleanup(self):
        """清理插件资源"""
        if hasattr(self, 'menu_action') and hasattr(self.main_window, 'tools_menu'):
            try:
                self.main_window.tools_menu.removeAction(self.menu_action)
            except Exception as e:
                print(f"清理波形对比插件菜单失败: {str(e)}")

        # 清理状态指示器
        if hasattr(self.main_window, 'status_bar'):
            try:
                # 清理标签
                if hasattr(self, 'status_label'):
                    self.status_label.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_label)

                # 清理按钮
                if hasattr(self, 'status_button'):
                    self.status_button.setVisible(False)
                    self.main_window.status_bar.removeWidget(self.status_button)
            except Exception as e:
                print(f"清理状态指示器失败: {str(e)}")

        # 关闭对话框
        if hasattr(self, 'current_dialog') and self.current_dialog:
            try:
                self.current_dialog.close()
            except Exception as e:
                print(f"关闭对话框失败: {str(e)}")

    def show_compare_dialog(self):
        """显示波形对比对话框"""
        # 创建非模态对话框
        dialog = QDialog(self.main_window)
        dialog.setWindowTitle("波形对比")
        dialog.resize(800, 600)
        # 设置窗口标志，添加最小化按钮，并确保窗口不会始终保持在最上层
        dialog.setWindowFlags(Qt.Window | Qt.WindowMinimizeButtonHint | Qt.WindowCloseButtonHint)

        # 保存对话框引用
        self.current_dialog = dialog

        layout = QVBoxLayout()
        layout.setSpacing(10)

        # 第一个波形文件选择
        wave1_layout = QHBoxLayout()
        wave1_label = QLabel("基准波形:")
        self.wave1_path = QLabel("未选择文件")
        wave1_btn = QPushButton("选择文件")
        wave1_btn.clicked.connect(lambda: self.select_wave_file(self.wave1_path))
        wave1_layout.addWidget(wave1_label)
        wave1_layout.addWidget(self.wave1_path, stretch=1)
        wave1_layout.addWidget(wave1_btn)

        # 第二个波形文件选择
        wave2_layout = QHBoxLayout()
        wave2_label = QLabel("对比波形:")
        self.wave2_path = QLabel("未选择文件")
        wave2_btn = QPushButton("选择文件")
        wave2_btn.clicked.connect(lambda: self.select_wave_file(self.wave2_path))
        wave2_layout.addWidget(wave2_label)
        wave2_layout.addWidget(self.wave2_path, stretch=1)
        wave2_layout.addWidget(wave2_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)

        # 结果显示区域
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 10pt;
                background-color: white;
                border: 1px solid #cccccc;
                border-radius: 3px;
            }
        """)

        # 按钮区域
        btn_layout = QHBoxLayout()

        # 比较按钮
        compare_btn = QPushButton("开始比较")
        compare_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        compare_btn.clicked.connect(lambda: self.compare_waves(dialog))

        # 最小化按钮
        minimize_button = QPushButton("最小化窗口")
        minimize_button.clicked.connect(dialog.showMinimized)

        # 后台运行按钮
        background_button = QPushButton("后台运行")
        background_button.clicked.connect(lambda: self.run_in_background(dialog))

        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                padding: 5px 15px;
                border-radius: 3px;
            }
        """)
        close_btn.clicked.connect(dialog.close)

        # 添加按钮到布局
        btn_layout.addWidget(compare_btn)
        btn_layout.addStretch()
        btn_layout.addWidget(minimize_button)
        btn_layout.addWidget(background_button)
        btn_layout.addWidget(close_btn)

        # 添加所有控件到主布局
        layout.addLayout(wave1_layout)
        layout.addLayout(wave2_layout)
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.result_text)
        layout.addLayout(btn_layout)

        dialog.setLayout(layout)

        # 连接对话框关闭事件
        dialog.finished.connect(self.on_dialog_closed)

        # 显示状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(True)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(True)

        # 非模态显示对话框
        dialog.show()

    def run_in_background(self, dialog):
        """将窗口隐藏到后台运行"""
        if dialog:
            # 隐藏窗口但不关闭
            dialog.hide()

            # 确保状态指示器可见
            if hasattr(self, 'status_label'):
                self.status_label.setVisible(True)
            if hasattr(self, 'status_button'):
                self.status_button.setVisible(True)

            # 在主窗口状态栏显示提示信息
            if hasattr(self.main_window, 'show_message'):
                self.main_window.show_message("波形对比器正在后台运行", 5000)

    def on_dialog_closed(self):
        """处理对话框关闭事件"""
        # 隐藏状态指示器
        if hasattr(self, 'status_label'):
            self.status_label.setVisible(False)
        if hasattr(self, 'status_button'):
            self.status_button.setVisible(False)

        # 清理对话框引用
        self.current_dialog = None

    def select_wave_file(self, label):
        """选择波形文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self.main_window,
            "选择波形文件",
            "",
            "波形文件 (*.vcd *.fsdb *.wlf);;所有文件 (*.*)"
        )
        if file_path:
            label.setText(file_path)

    def compare_waves(self, dialog):
        """比较两个波形文件"""
        wave1_path = self.wave1_path.text()
        wave2_path = self.wave2_path.text()

        if wave1_path == "未选择文件" or wave2_path == "未选择文件":
            QMessageBox.warning(dialog, "警告", "请先选择两个波形文件")
            return

        if not os.path.exists(wave1_path) or not os.path.exists(wave2_path):
            QMessageBox.warning(dialog, "警告", "所选文件不存在")
            return

        try:
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.result_text.clear()

            # 调用verdi进行波形比较
            result = self.compare_with_verdi(wave1_path, wave2_path)

            # 解析比较结果
            self.analyze_compare_result(result)

        except Exception as e:
            QMessageBox.critical(dialog, "错误", f"波形比较失败: {str(e)}")
        finally:
            self.progress_bar.setVisible(False)

    def compare_with_verdi(self, wave1_path, wave2_path):
        """使用verdi命令行工具比较波形"""
        # 创建临时工作目录
        work_dir = os.path.join(os.path.dirname(wave1_path), "verdi_compare_temp")
        if not os.path.exists(work_dir):
            os.makedirs(work_dir)

        try:
            # 创建详细的调试日志文件
            debug_log = os.path.join(work_dir, "verdi_debug.log")

            # 修改 TCL 脚本内容
            script_content = f"""
            # 设置调试模式
            set env(VERDI_DEBUG) 1
            set env(NOVAS_AUTO_EXIT) 1
            set env(NOVAS_LOG_FILE) "{debug_log}"
            set env(NOVAS_FSDB_SUPPORT) 1

            # 导入波形文件
            if [catch {{
                puts "正在导入基准波形: {wave1_path}"
                set base_wave [debImport -2001 "{wave1_path}"]
                puts "正在导入对比波形: {wave2_path}"
                set test_wave [debImport -2001 "{wave2_path}"]
            }} result] {{
                puts "错误: 导入波形失败 - $result"
                exit 1
            }}

            # 创建波形窗口并加载波形
            if [catch {{
                puts "创建波形窗口"
                set wave_window [wvCreateWindow]
                verdiWindowWorkMode -win $wave_window Wave
                wvResizeWindow -win $wave_window 0 23 1920 1017

                # 将导入的波形加载到窗口
                puts "加载波形到窗口"
                wvSetCurrentWindow -win $wave_window
                wvOpenFile -win $wave_window -fsdb "{wave1_path}"

                # 等待波形加载完成
                after 2000

                # 添加所有信号到波形窗口
                puts "添加信号到波形窗口"
                wvGetAllSignals -win $wave_window
                wvAddAllSignals -win $wave_window

                # 确保所有信号被选中
                wvSelectAllSignals -win $wave_window

                # 检查信号是否成功添加
                set signal_count [wvGetSignalCount -win $wave_window]
                puts "加载的信号数量: $signal_count"
                if {{$signal_count == 0}} {{
                    error "未能加载任何信号"
                }}

                # 刷新波形显示
                wvZoomAll -win $wave_window
            }} result] {{
                puts "错误: 创建波形窗口或添加信号失败 - $result"
                exit 1
            }}

            # 执行波形比较
            if [catch {{
                puts "开始比较波形"
                after 1000  # 等待信号加载完成
                wvCompareSelectedToFile -win $wave_window -file {wave2_path}
                puts "生成差异报告"
                wvCompareResult -win $wave_window -file "{os.path.join(work_dir, 'wave_diff.rpt')}"
            }} result] {{
                puts "错误: 波形比较失败 - $result"
                exit 1
            }}

            # 等待比较完成
            after 5000

            # 退出前保存会话
            puts "保存会话配置"
            if [catch {{
                verdiSaveSession -nWave "{os.path.join(work_dir, 'wave_session.tcl')}"
            }} result] {{
                puts "警告: 保存会话失败 - $result"
            }}

            # 完成后退出
            puts "比较完成，正常退出"
            debExit
            """

            script_path = os.path.join(work_dir, "wave_compare.tcl")
            with open(script_path, "w") as f:
                f.write(script_content)

            self.progress_bar.setValue(20)

            # 设置环境变量
            env = os.environ.copy()
            env['VERDI_SKIP_DBCHECK'] = '1'
            env['NOVAS_FSDB_SUPPORT'] = '1'
            env['VERDI_DEBUG'] = '1'

            # 修改命令行参数
            cmd = [
                "verdi",
                "-play", script_path,
                "-ssf", wave1_path,
                "-ssf", wave2_path,
                "-rcFile", os.path.join(work_dir, "novas.rc"),
                "-logfile", os.path.join(work_dir, "verdi.log")
            ]

            self.progress_bar.setValue(40)

            # 运行命令并捕获输出
            try:
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    env=env,
                    cwd=work_dir
                )

                # 等待进程完成，设置超时时间
                stdout, stderr = process.communicate(timeout=60)

                # 记录命令输出到日志
                with open(os.path.join(work_dir, "command_output.log"), "w") as f:
                    f.write(f"命令输出:\n{stdout}\n\n错误输出:\n{stderr}")

                if process.returncode != 0:
                    raise Exception(f"Verdi退出码: {process.returncode}\n{stderr}")

                self.progress_bar.setValue(60)

                # 检查差异报告是否生成
                diff_file = os.path.join(work_dir, "wave_diff.rpt")
                if os.path.exists(diff_file):
                    with open(diff_file, "r") as f:
                        diff_content = f.read()
                    return diff_content
                else:
                    # 如果没有生成差异报告，返回调试信息
                    debug_info = ""
                    if os.path.exists(debug_log):
                        with open(debug_log, "r") as f:
                            debug_info = f.read()
                    raise Exception(f"未生成差异报告。调试信息:\n{debug_info}")

            except subprocess.TimeoutExpired:
                process.kill()
                raise Exception("Verdi执行超时")

        except Exception as e:
            raise Exception(f"执行Verdi命令失败:\n{str(e)}")
        finally:
            # 保留日志文件以供调试，但删除其他临时文件
            try:
                if os.path.exists(work_dir):
                    for f in os.listdir(work_dir):
                        if not f.endswith('.log') and not f.endswith('.rpt'):
                            os.remove(os.path.join(work_dir, f))
            except Exception as e:
                print(f"清理临时文件失败: {str(e)}")

    def analyze_compare_result(self, result):
        """分析比较结果并显示"""
        try:
            if not result:
                raise Exception("没有获取到比较结果")

            self.result_text.append("波形比较结果分析：\n")
            self.result_text.append("-" * 50 + "\n")

            # 解析差异报告
            value_diff = []  # 值不同的信号
            timing_diff = []  # 时序不同的信号

            # 使用正则表达式匹配差异信息
            signal_pattern = r'Signal:\s+(\S+)\s+Difference type:\s+(\w+)'
            differences = re.finditer(signal_pattern, result)

            for match in differences:
                signal_name = match.group(1)
                diff_type = match.group(2)

                if diff_type == "Value":
                    value_diff.append(signal_name)
                elif diff_type == "Timing":
                    timing_diff.append(signal_name)

            # 显示统计信息
            self.result_text.append(f"差异信号统计：\n")
            self.result_text.append(f"1. 值不同的信号数量：{len(value_diff)}个\n")
            self.result_text.append(f"2. 时序不同的信号数量：{len(timing_diff)}个\n\n")

            # 显示详细差异信息
            if value_diff:
                self.result_text.append("值不同的信号：\n")
                for signal in value_diff:
                    self.result_text.append(f"  - {signal}\n")
                self.result_text.append("\n")

            if timing_diff:
                self.result_text.append("时序不同的信号：\n")
                for signal in timing_diff:
                    self.result_text.append(f"  - {signal}\n")
                self.result_text.append("\n")

            # 添加具体的时间点差异信息
            time_diff_pattern = r'Time:\s+(\d+\s*\w*)\s+Base value:\s+(\S+)\s+Test value:\s+(\S+)'
            time_diffs = re.finditer(time_diff_pattern, result)

            if time_diffs:
                self.result_text.append("具体差异点：\n")
                for diff in time_diffs:
                    time = diff.group(1)
                    base_val = diff.group(2)
                    test_val = diff.group(3)
                    self.result_text.append(f"  时间点: {time}\n")
                    self.result_text.append(f"    - 基准值: {base_val}\n")
                    self.result_text.append(f"    - 测试值: {test_val}\n")

            self.progress_bar.setValue(100)

        except Exception as e:
            self.result_text.append(f"结果分析失败: {str(e)}")