<div class="row">
    <!-- 基本信息 -->
    <div class="col-md-6">
        <h6 class="text-primary mb-3"><i class="fas fa-info-circle me-1"></i>基本信息</h6>
        <table class="table table-sm">
            <tr>
                <td width="30%" class="fw-bold">用例名称:</td>
                <td>{{ testcase.testcase_name }}</td>
            </tr>
            <tr>
                <td class="fw-bold">测试分类:</td>
                <td><span class="badge bg-secondary">{{ testcase.test_category }}</span></td>
            </tr>
            <tr>
                <td class="fw-bold">测试项:</td>
                <td>{{ testcase.items }}</td>
            </tr>
            <tr>
                <td class="fw-bold">测试区域:</td>
                <td>{{ testcase.test_areas }}</td>
            </tr>
            <tr>
                <td class="fw-bold">功能点:</td>
                <td>{{ testcase.function_points }}</td>
            </tr>
            <tr>
                <td class="fw-bold">负责人:</td>
                <td>{{ testcase.owner }}</td>
            </tr>
            <tr>
                <td class="fw-bold">所属项目:</td>
                <td>{{ testcase.testplan.project.name }}</td>
            </tr>
            <tr>
                <td class="fw-bold">测试计划:</td>
                <td>{{ testcase.testplan.name }}</td>
            </tr>
        </table>
    </div>
    
    <!-- 执行状态 -->
    <div class="col-md-6">
        <h6 class="text-primary mb-3"><i class="fas fa-tasks me-1"></i>执行状态</h6>
        <table class="table table-sm">
            <tr>
                <td width="40%" class="fw-bold">子系统状态:</td>
                <td>
                    <span class="badge status-{{ testcase.subsys_status }}">
                        {{ testcase.get_subsys_status_display }}
                    </span>
                    {% if testcase.subsys_phase %}
                        <small class="text-muted d-block">阶段: {{ testcase.subsys_phase }}</small>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="fw-bold">TOP状态:</td>
                <td>
                    <span class="badge status-{{ testcase.top_status }}">
                        {{ testcase.get_top_status_display }}
                    </span>
                    {% if testcase.top_phase %}
                        <small class="text-muted d-block">阶段: {{ testcase.top_phase }}</small>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="fw-bold">后仿子系统:</td>
                <td>
                    <span class="badge status-{{ testcase.post_subsys_status }}">
                        {{ testcase.get_post_subsys_status_display }}
                    </span>
                    {% if testcase.post_subsys_phase %}
                        <small class="text-muted d-block">阶段: {{ testcase.post_subsys_phase }}</small>
                    {% endif %}
                </td>
            </tr>
            <tr>
                <td class="fw-bold">后仿TOP:</td>
                <td>
                    <span class="badge status-{{ testcase.post_top_status }}">
                        {{ testcase.get_post_top_status_display }}
                    </span>
                    {% if testcase.post_top_phase %}
                        <small class="text-muted d-block">阶段: {{ testcase.post_top_phase }}</small>
                    {% endif %}
                </td>
            </tr>
        </table>
    </div>
</div>

<!-- 测试详情 -->
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary mb-3"><i class="fas fa-clipboard-list me-1"></i>测试详情</h6>
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="fw-bold">测试范围:</label>
                    <p class="text-muted">{{ testcase.test_scope|default:"未填写" }}</p>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">检查点:</label>
                    <p class="text-muted">{{ testcase.check_point|default:"未填写" }}</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="fw-bold">覆盖:</label>
                    <p class="text-muted">{{ testcase.cover|default:"未填写" }}</p>
                </div>
                <div class="mb-3">
                    <label class="fw-bold">备注:</label>
                    <p class="text-muted">{{ testcase.note|default:"无" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 时间记录 -->
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary mb-3"><i class="fas fa-clock me-1"></i>时间记录</h6>
        <div class="row">
            <div class="col-md-4">
                <div class="text-center">
                    <div class="fw-bold">开始时间</div>
                    <div class="text-muted">
                        {% if testcase.start_time %}
                            {{ testcase.start_time|date:"Y-m-d H:i:s" }}
                        {% else %}
                            未开始
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="fw-bold">结束时间</div>
                    <div class="text-muted">
                        {% if testcase.end_time %}
                            {{ testcase.end_time|date:"Y-m-d H:i:s" }}
                        {% else %}
                            未结束
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="fw-bold">执行时长</div>
                    <div class="text-muted">
                        {% if testcase.actual_time %}
                            {{ testcase.actual_time }}
                        {% else %}
                            未计算
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 执行历史 -->
{% if execution_records %}
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary mb-3"><i class="fas fa-history me-1"></i>执行历史</h6>
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>执行时间</th>
                        <th>执行命令</th>
                        <th>结果</th>
                        <th>执行时长</th>
                        <th>执行者</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in execution_records|slice:":10" %}
                        <tr>
                            <td>{{ record.start_time|date:"m-d H:i" }}</td>
                            <td>
                                <code class="small">{{ record.command|truncatechars:40 }}</code>
                            </td>
                            <td>
                                {% if record.result == 'pass' %}
                                    <span class="badge bg-success">通过</span>
                                {% elif record.result == 'fail' %}
                                    <span class="badge bg-danger">失败</span>
                                {% elif record.result == 'running' %}
                                    <span class="badge bg-primary">运行中</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ record.get_result_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if record.duration %}
                                    {{ record.duration }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td>{{ record.executor.username }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% if execution_records.count > 10 %}
                <p class="text-muted small">显示最近10条记录，共{{ execution_records.count }}条</p>
            {% endif %}
        </div>
    </div>
</div>
{% else %}
<div class="row mt-3">
    <div class="col-12">
        <h6 class="text-primary mb-3"><i class="fas fa-history me-1"></i>执行历史</h6>
        <div class="text-center text-muted py-3">
            <i class="fas fa-inbox fa-2x mb-2"></i>
            <p>暂无执行记录</p>
        </div>
    </div>
</div>
{% endif %}
