{% extends 'dashboard/base.html' %}

{% block title %}项目管理 - RunSim 仪表盘系统{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2><i class="fas fa-project-diagram me-2"></i>项目管理</h2>
    <a href="/admin/dashboard/project/add/" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>新建项目
    </a>
</div>

<!-- 搜索和过滤 -->
<div class="filter-section">
    <div class="row">
        <div class="col-md-6">
            <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="searchInput" placeholder="搜索项目名称或描述...">
            </div>
        </div>
        <div class="col-md-3">
            <select class="form-select status-filter">
                <option value="">所有状态</option>
                <option value="kickoff">Kickoff</option>
                <option value="pre_rtl_0_1">PreRTL0.1</option>
                <option value="rtl_0_1">RTL0.1</option>
                <option value="pre_rtl_0_5">PreRTL0.5</option>
                <option value="rtl_0_5">RTL0.5</option>
                <option value="pre_rtl_0_9">PreRTL0.9</option>
                <option value="rtl_0_9">RTL0.9</option>
                <option value="post_sim">后仿</option>
            </select>
        </div>
        <div class="col-md-3">
            <select class="form-select verification-filter">
                <option value="">所有验证阶段</option>
                <option value="dvr1">DVR1</option>
                <option value="dvr2">DVR2</option>
                <option value="dvr3">DVR3</option>
                <option value="dvs1">DVS1</option>
                <option value="dvs2">DVS2</option>
            </select>
        </div>
    </div>
</div>

<!-- 项目列表 -->
<div class="card">
    <div class="card-body">
        {% if page_obj %}
            <div class="table-responsive">
                <table class="table table-hover searchable filterable">
                    <thead>
                        <tr>
                            <th>项目名称</th>
                            <th>负责人</th>
                            <th>SOC阶段</th>
                            <th>验证阶段</th>
                            <th>创建时间</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for project in page_obj %}
                            <tr data-status="{{ project.current_soc_phase }}" data-verification="{{ project.current_verification_phase }}">
                                <td>
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="{% url 'dashboard:project_detail' project.id %}" class="text-decoration-none">
                                                {{ project.name }}
                                            </a>
                                        </h6>
                                        <small class="text-muted">{{ project.description|truncatechars:50 }}</small>
                                    </div>
                                </td>
                                <td>{{ project.owner.username }}</td>
                                <td>
                                    <span class="badge bg-primary">{{ project.get_current_soc_phase_display }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ project.get_current_verification_phase_display }}</span>
                                </td>
                                <td>{{ project.created_at|date:"Y-m-d H:i" }}</td>
                                <td>{{ project.updated_at|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{% url 'dashboard:project_detail' project.id %}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="/admin/dashboard/project/{{ project.id }}/change/" 
                                           class="btn btn-outline-secondary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            {% if page_obj.has_other_pages %}
                <nav aria-label="项目分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-project-diagram fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无项目</h5>
                <p class="text-muted">点击上方"新建项目"按钮创建第一个项目</p>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 状态过滤
    $('.status-filter').change(function() {
        var status = $(this).val();
        filterTable('data-status', status);
    });

    // 验证阶段过滤
    $('.verification-filter').change(function() {
        var verification = $(this).val();
        filterTable('data-verification', verification);
    });

    function filterTable(attribute, value) {
        if (value === '') {
            $('.filterable tbody tr').show();
        } else {
            $('.filterable tbody tr').hide();
            $('.filterable tbody tr[' + attribute + '="' + value + '"]').show();
        }
    }

    // 搜索功能
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('.searchable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
        });
    });
});
</script>
{% endblock %}
